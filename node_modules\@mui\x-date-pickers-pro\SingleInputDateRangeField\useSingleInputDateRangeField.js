"use strict";
'use client';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useSingleInputDateRangeField = void 0;
var _internals = require("@mui/x-date-pickers/internals");
var _managers = require("../managers");
const useSingleInputDateRangeField = props => {
  const manager = (0, _managers.useDateRangeManager)(props);
  return (0, _internals.useField)({
    manager,
    props
  });
};
exports.useSingleInputDateRangeField = useSingleInputDateRangeField;