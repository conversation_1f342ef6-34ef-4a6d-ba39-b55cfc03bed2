import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Box,
  Grid,
  CircularProgress,
  Alert,
  InputAdornment,
  Card,
  CardContent,
  Avatar,
  IconButton,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Person as PersonIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { CustomerWithCurrentApartment, CreateCustomerRequest, UpdateCustomerRequest } from '../../../types/Customer';
import { CustomerService } from '../../services/customerService';

interface CustomerFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: CreateCustomerRequest | UpdateCustomerRequest) => Promise<void>;
  customer?: CustomerWithCurrentApartment; // For edit mode
  loading?: boolean;
}

interface FormData {
  name: string;
  address: string;
  mobile: string;
  occupation: string;
  startDate: Dayjs | null;
  rent: string;
  advance: string;
  nid?: File;
  photo?: File;
}

const CustomerForm: React.FC<CustomerFormProps> = ({
  open,
  onClose,
  onSubmit,
  customer,
  loading = false,
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    address: '',
    mobile: '',
    occupation: '',
    startDate: dayjs(),
    rent: '',
    advance: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [nidPreview, setNidPreview] = useState<string>('');
  const [photoPreview, setPhotoPreview] = useState<string>('');

  const isEditMode = !!customer;

  // Initialize form data when customer prop changes
  useEffect(() => {
    if (customer) {
      setFormData({
        name: customer.name,
        address: customer.address,
        mobile: customer.mobile,
        occupation: customer.occupation,
        startDate: dayjs(customer.startDate),
        rent: customer.rent.toString(),
        advance: customer.advance.toString(),
      });
    } else {
      setFormData({
        name: '',
        address: '',
        mobile: '',
        occupation: '',
        startDate: dayjs(),
        rent: '',
        advance: '',
      });
    }
    setErrors({});
    setNidPreview('');
    setPhotoPreview('');
  }, [customer, open]);

  const handleInputChange = (field: keyof FormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const value = event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleNumberInputChange = (field: keyof FormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value;
    // Only allow positive numbers
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setFormData(prev => ({ ...prev, [field]: value }));
      
      // Clear error when user starts typing
      if (errors[field]) {
        setErrors(prev => ({ ...prev, [field]: '' }));
      }
    }
  };

  const handleDateChange = (date: Dayjs | null) => {
    setFormData(prev => ({ ...prev, startDate: date }));
    
    // Clear error when date is selected
    if (errors.startDate) {
      setErrors(prev => ({ ...prev, startDate: '' }));
    }
  };

  const handleFileChange = (field: 'nid' | 'photo') => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, [field]: file }));
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        if (field === 'nid') {
          setNidPreview(result);
        } else {
          setPhotoPreview(result);
        }
      };
      reader.readAsDataURL(file);
      
      // Clear error
      if (errors[field]) {
        setErrors(prev => ({ ...prev, [field]: '' }));
      }
    }
  };

  const removeFile = (field: 'nid' | 'photo') => {
    setFormData(prev => ({ ...prev, [field]: undefined }));
    if (field === 'nid') {
      setNidPreview('');
    } else {
      setPhotoPreview('');
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate name
    if (!formData.name.trim()) {
      newErrors.name = 'Customer name is required';
    } else if (formData.name.trim().length > 100) {
      newErrors.name = 'Customer name must be less than 100 characters';
    }

    // Validate address
    if (!formData.address.trim()) {
      newErrors.address = 'Address is required';
    } else if (formData.address.trim().length > 200) {
      newErrors.address = 'Address must be less than 200 characters';
    }

    // Validate mobile
    if (!formData.mobile.trim()) {
      newErrors.mobile = 'Mobile number is required';
    } else if (!/^[\+]?[1-9][\d]{0,15}$/.test(formData.mobile.trim())) {
      newErrors.mobile = 'Please enter a valid mobile number';
    }

    // Validate occupation
    if (!formData.occupation.trim()) {
      newErrors.occupation = 'Occupation is required';
    } else if (formData.occupation.trim().length > 100) {
      newErrors.occupation = 'Occupation must be less than 100 characters';
    }

    // Validate start date
    if (!formData.startDate) {
      newErrors.startDate = 'Start date is required';
    }

    // Validate rent
    if (!formData.rent.trim()) {
      newErrors.rent = 'Rent amount is required';
    } else {
      const rent = parseFloat(formData.rent);
      if (isNaN(rent) || rent <= 0) {
        newErrors.rent = 'Rent amount must be a valid positive number';
      } else if (rent > 1000000) {
        newErrors.rent = 'Rent amount cannot exceed 1,000,000';
      }
    }

    // Validate advance
    if (!formData.advance.trim()) {
      newErrors.advance = 'Advance amount is required';
    } else {
      const advance = parseFloat(formData.advance);
      if (isNaN(advance) || advance < 0) {
        newErrors.advance = 'Advance amount must be a valid positive number';
      } else if (advance > 1000000) {
        newErrors.advance = 'Advance amount cannot exceed 1,000,000';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const submitData = isEditMode
        ? {
            id: customer!.id,
            name: formData.name.trim(),
            address: formData.address.trim(),
            mobile: formData.mobile.trim(),
            occupation: formData.occupation.trim(),
            startDate: formData.startDate!.toDate(),
            rent: parseFloat(formData.rent),
            advance: parseFloat(formData.advance),
            nid: formData.nid,
            photo: formData.photo,
          } as UpdateCustomerRequest
        : {
            name: formData.name.trim(),
            address: formData.address.trim(),
            mobile: formData.mobile.trim(),
            occupation: formData.occupation.trim(),
            startDate: formData.startDate!.toDate(),
            rent: parseFloat(formData.rent),
            advance: parseFloat(formData.advance),
            nid: formData.nid,
            photo: formData.photo,
          } as CreateCustomerRequest;

      await onSubmit(submitData);
    } catch (error) {
      console.error('Error submitting customer form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      address: '',
      mobile: '',
      occupation: '',
      startDate: dayjs(),
      rent: '',
      advance: '',
    });
    setErrors({});
    setNidPreview('');
    setPhotoPreview('');
    setIsSubmitting(false);
    onClose();
  };

  const FileUploadCard: React.FC<{
    title: string;
    file?: File;
    preview: string;
    onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
    onRemove: () => void;
    accept: string;
  }> = ({ title, file, preview, onFileChange, onRemove, accept }) => (
    <Card variant="outlined" sx={{ height: '100%' }}>
      <CardContent sx={{ textAlign: 'center', p: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          {title}
        </Typography>
        
        {preview ? (
          <Box sx={{ position: 'relative', display: 'inline-block' }}>
            <Avatar
              src={preview}
              sx={{ width: 80, height: 80, mx: 'auto', mb: 1 }}
            />
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
              <IconButton size="small" onClick={onRemove} color="error">
                <DeleteIcon />
              </IconButton>
              <IconButton size="small" onClick={() => window.open(preview, '_blank')}>
                <ViewIcon />
              </IconButton>
            </Box>
          </Box>
        ) : (
          <Box>
            <Avatar sx={{ width: 80, height: 80, mx: 'auto', mb: 1, bgcolor: 'grey.200' }}>
              <PersonIcon />
            </Avatar>
            <input
              accept={accept}
              style={{ display: 'none' }}
              id={`${title.toLowerCase()}-upload`}
              type="file"
              onChange={onFileChange}
            />
            <label htmlFor={`${title.toLowerCase()}-upload`}>
              <Button
                variant="outlined"
                component="span"
                startIcon={<UploadIcon />}
                size="small"
              >
                Upload
              </Button>
            </label>
          </Box>
        )}
        
        {file && (
          <Typography variant="caption" display="block" sx={{ mt: 1 }}>
            {file.name}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
          {isEditMode ? 'Edit Customer' : 'Create New Customer'}
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <Grid container spacing={3}>
            {/* Personal Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Personal Information
              </Typography>
            </Grid>

            {/* Customer Name */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Customer Name"
                value={formData.name}
                onChange={handleInputChange('name')}
                error={!!errors.name}
                helperText={errors.name}
                placeholder="e.g., John Doe"
              />
            </Grid>

            {/* Mobile Number */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Mobile Number"
                value={formData.mobile}
                onChange={handleInputChange('mobile')}
                error={!!errors.mobile}
                helperText={errors.mobile}
                placeholder="e.g., ******-0123"
              />
            </Grid>

            {/* Address */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                value={formData.address}
                onChange={handleInputChange('address')}
                error={!!errors.address}
                helperText={errors.address}
                multiline
                rows={2}
                placeholder="e.g., 123 Main Street, City, State"
              />
            </Grid>

            {/* Occupation */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Occupation"
                value={formData.occupation}
                onChange={handleInputChange('occupation')}
                error={!!errors.occupation}
                helperText={errors.occupation}
                placeholder="e.g., Software Engineer"
              />
            </Grid>

            {/* Start Date */}
            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Start Date"
                value={formData.startDate}
                onChange={handleDateChange}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.startDate,
                    helperText: errors.startDate,
                  },
                }}
              />
            </Grid>

            {/* Financial Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Financial Information
              </Typography>
            </Grid>

            {/* Rent Amount */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Monthly Rent"
                value={formData.rent}
                onChange={handleNumberInputChange('rent')}
                error={!!errors.rent}
                helperText={errors.rent}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
                placeholder="1200"
              />
            </Grid>

            {/* Advance Amount */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Advance Amount"
                value={formData.advance}
                onChange={handleNumberInputChange('advance')}
                error={!!errors.advance}
                helperText={errors.advance}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
                placeholder="2400"
              />
            </Grid>

            {/* Document Uploads */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Documents (Optional)
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FileUploadCard
                title="NID Document"
                file={formData.nid}
                preview={nidPreview}
                onFileChange={handleFileChange('nid')}
                onRemove={() => removeFile('nid')}
                accept="image/*,.pdf"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FileUploadCard
                title="Customer Photo"
                file={formData.photo}
                preview={photoPreview}
                onFileChange={handleFileChange('photo')}
                onRemove={() => removeFile('photo')}
                accept="image/*"
              />
            </Grid>

            {/* Info Alert */}
            <Grid item xs={12}>
              <Alert severity="info" sx={{ mt: 1 }}>
                {isEditMode
                  ? 'Update customer information. Documents are optional and will only be updated if new files are uploaded.'
                  : 'Create a new customer profile. All fields except documents are required.'}
              </Alert>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={isSubmitting || loading}
          startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
        >
          {isSubmitting ? 'Saving...' : isEditMode ? 'Update Customer' : 'Create Customer'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CustomerForm;
