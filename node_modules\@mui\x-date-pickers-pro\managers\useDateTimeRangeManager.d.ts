import type { MakeOptional } from '@mui/x-internals/types';
import { PickerManager } from '@mui/x-date-pickers/models';
import { AmPmProps, PickerRangeValue, UseFieldInternalProps } from '@mui/x-date-pickers/internals';
import { DateTimeRangeValidationError, RangeFieldSeparatorProps } from "../models/index.js";
import { ExportedValidateDateTimeRangeProps, ValidateDateTimeRangeProps } from "../validation/validateDateTimeRange.js";
export declare function useDateTimeRangeManager<TEnableAccessibleFieldDOMStructure extends boolean = true>(parameters?: UseDateTimeRangeManagerParameters<TEnableAccessibleFieldDOMStructure>): UseDateTimeRangeManagerReturnValue<TEnableAccessibleFieldDOMStructure>;
export interface UseDateTimeRangeManagerParameters<TEnableAccessibleFieldDOMStructure extends boolean> extends RangeFieldSeparatorProps {
  enableAccessibleFieldDOMStructure?: TEnableAccessibleFieldDOMStructure;
}
export type UseDateTimeRangeManagerReturnValue<TEnableAccessibleFieldDOMStructure extends boolean> = PickerManager<PickerRangeValue, TEnableAccessibleFieldDOMStructure, DateTimeRangeValidationError, ValidateDateTimeRangeProps, DateTimeRangeManagerFieldInternalProps<TEnableAccessibleFieldDOMStructure>>;
export interface DateTimeRangeManagerFieldInternalProps<TEnableAccessibleFieldDOMStructure extends boolean> extends MakeOptional<UseFieldInternalProps<PickerRangeValue, TEnableAccessibleFieldDOMStructure, DateTimeRangeValidationError>, 'format'>, ExportedValidateDateTimeRangeProps, AmPmProps, RangeFieldSeparatorProps {}