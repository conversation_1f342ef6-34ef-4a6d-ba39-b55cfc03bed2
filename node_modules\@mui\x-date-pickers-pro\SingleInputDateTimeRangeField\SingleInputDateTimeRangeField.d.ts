import * as React from 'react';
import { SingleInputDateTimeRangeFieldProps } from "./SingleInputDateTimeRangeField.types.js";
import { FieldType } from "../models/index.js";
type DateRangeFieldComponent = (<TEnableAccessibleFieldDOMStructure extends boolean = true>(props: SingleInputDateTimeRangeFieldProps<TEnableAccessibleFieldDOMStructure> & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
  fieldType?: FieldType;
};
/**
 * Demos:
 *
 * - [DateTimeRangeField](http://mui.com/x/react-date-pickers/date-time-range-field/)
 * - [Fields](https://mui.com/x/react-date-pickers/fields/)
 *
 * API:
 *
 * - [SingleInputDateTimeRangeField API](https://mui.com/x/api/single-input-date-time-range-field/)
 */
declare const SingleInputDateTimeRangeField: DateRangeFieldComponent;
export { SingleInputDateTimeRangeField };