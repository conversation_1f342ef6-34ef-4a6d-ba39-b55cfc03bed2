"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PickersRangeCalendarHeader = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _propTypes = _interopRequireDefault(require("prop-types"));
var _styles = require("@mui/material/styles");
var _PickersCalendarHeader = require("@mui/x-date-pickers/PickersCalendarHeader");
var _internals = require("@mui/x-date-pickers/internals");
var _hooks = require("@mui/x-date-pickers/hooks");
var _jsxRuntime = require("react/jsx-runtime");
const _excluded = ["calendars", "month", "monthIndex", "labelId"],
  _excluded2 = ["format", "slots", "slotProps", "currentMonth", "onMonthChange", "disableFuture", "disablePast", "minDate", "maxDate", "timezone", "reduceAnimations", "views", "view"];
const PickersRangeCalendarHeaderContentMultipleCalendars = (0, _styles.styled)(_internals.PickersArrowSwitcher)({
  padding: '12px 16px 4px 16px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between'
});
const PickersRangeCalendarHeader = exports.PickersRangeCalendarHeader = /*#__PURE__*/React.forwardRef(function PickersRangeCalendarHeader(props, ref) {
  const adapter = (0, _hooks.usePickerAdapter)();
  const translations = (0, _hooks.usePickerTranslations)();
  const {
      calendars,
      month,
      monthIndex,
      labelId
    } = props,
    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  const {
      format,
      slots,
      slotProps,
      currentMonth,
      onMonthChange,
      disableFuture,
      disablePast,
      minDate,
      maxDate,
      timezone
      // omit props that are not used in the PickersArrowSwitcher
    } = other,
    otherRangeProps = (0, _objectWithoutPropertiesLoose2.default)(other, _excluded2);
  const isNextMonthDisabled = (0, _internals.useNextMonthDisabled)(currentMonth, {
    disableFuture,
    maxDate,
    timezone
  });
  const isPreviousMonthDisabled = (0, _internals.usePreviousMonthDisabled)(currentMonth, {
    disablePast,
    minDate,
    timezone
  });
  if (calendars === 1) {
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_PickersCalendarHeader.PickersCalendarHeader, (0, _extends2.default)({}, other, {
      labelId: labelId,
      ref: ref
    }));
  }
  const selectNextMonth = () => onMonthChange(adapter.addMonths(currentMonth, 1));
  const selectPreviousMonth = () => onMonthChange(adapter.addMonths(currentMonth, -1));
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(PickersRangeCalendarHeaderContentMultipleCalendars, (0, _extends2.default)({}, otherRangeProps, {
    ref: ref,
    onGoToPrevious: selectPreviousMonth,
    onGoToNext: selectNextMonth,
    isPreviousHidden: monthIndex !== 0,
    isPreviousDisabled: isPreviousMonthDisabled,
    previousLabel: translations.previousMonth,
    isNextHidden: monthIndex !== calendars - 1,
    isNextDisabled: isNextMonthDisabled,
    nextLabel: translations.nextMonth,
    slots: slots,
    slotProps: slotProps,
    labelId: labelId,
    children: adapter.formatByString(month, format ?? `${adapter.formats.month} ${adapter.formats.year}`)
  }));
});
if (process.env.NODE_ENV !== "production") PickersRangeCalendarHeader.displayName = "PickersRangeCalendarHeader";
process.env.NODE_ENV !== "production" ? PickersRangeCalendarHeader.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The number of calendars rendered.
   */
  calendars: _propTypes.default.oneOf([1, 2, 3]).isRequired,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: _propTypes.default.object,
  className: _propTypes.default.string,
  currentMonth: _propTypes.default.object.isRequired,
  disabled: _propTypes.default.bool,
  disableFuture: _propTypes.default.bool,
  disablePast: _propTypes.default.bool,
  /**
   * Format used to display the date.
   * @default `${adapter.formats.month} ${adapter.formats.year}`
   */
  format: _propTypes.default.string,
  /**
   * Id of the calendar text element.
   * It is used to establish an `aria-labelledby` relationship with the calendar `grid` element.
   */
  labelId: _propTypes.default.string,
  maxDate: _propTypes.default.object.isRequired,
  minDate: _propTypes.default.object.isRequired,
  /**
   * Month used for this header.
   */
  month: _propTypes.default.object.isRequired,
  /**
   * Index of the month used for this header.
   */
  monthIndex: _propTypes.default.number.isRequired,
  onMonthChange: _propTypes.default.func.isRequired,
  onViewChange: _propTypes.default.func,
  reduceAnimations: _propTypes.default.bool.isRequired,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: _propTypes.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: _propTypes.default.object,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object]),
  timezone: _propTypes.default.string.isRequired,
  view: _propTypes.default.oneOf(['day', 'month', 'year']).isRequired,
  views: _propTypes.default.arrayOf(_propTypes.default.oneOf(['day', 'month', 'year']).isRequired).isRequired
} : void 0;