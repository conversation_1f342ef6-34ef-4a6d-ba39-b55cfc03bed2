import type { MakeOptional } from '@mui/x-internals/types';
import { PickerManager } from '@mui/x-date-pickers/models';
import { PickerRangeValue, UseFieldInternalProps } from '@mui/x-date-pickers/internals';
import { DateRangeValidationError, RangeFieldSeparatorProps } from "../models/index.js";
import { ExportedValidateDateRangeProps, ValidateDateRangeProps } from "../validation/validateDateRange.js";
export declare function useDateRangeManager<TEnableAccessibleFieldDOMStructure extends boolean = true>(parameters?: UseDateRangeManagerParameters<TEnableAccessibleFieldDOMStructure>): UseDateRangeManagerReturnValue<TEnableAccessibleFieldDOMStructure>;
export interface UseDateRangeManagerParameters<TEnableAccessibleFieldDOMStructure extends boolean> extends RangeFieldSeparatorProps {
  enableAccessibleFieldDOMStructure?: TEnableAccessibleFieldDOMStructure;
}
export type UseDateRangeManagerReturnValue<TEnableAccessibleFieldDOMStructure extends boolean> = PickerManager<PickerRangeValue, TEnableAccessibleFieldDOMStructure, DateRangeValidationError, ValidateDateRangeProps, DateRangeManagerFieldInternalProps<TEnableAccessibleFieldDOMStructure>>;
export interface DateRangeManagerFieldInternalProps<TEnableAccessibleFieldDOMStructure extends boolean> extends MakeOptional<UseFieldInternalProps<PickerRangeValue, TEnableAccessibleFieldDOMStructure, DateRangeValidationError>, 'format'>, RangeFieldSeparatorProps, ExportedValidateDateRangeProps {}