import * as React from 'react';
import { TimeClock } from "../TimeClock/index.js";
import { DigitalClock } from "../DigitalClock/index.js";
import { MultiSectionDigitalClock } from "../MultiSectionDigitalClock/index.js";
import { isInternalTimeView, isTimeView } from "../internals/utils/time-utils.js";
import { jsx as _jsx } from "react/jsx-runtime";
export const renderTimeViewClock = ({
  view,
  onViewChange,
  focusedView,
  onFocusedViewChange,
  views,
  value,
  defaultValue,
  referenceDate,
  onChange,
  className,
  classes,
  disableFuture,
  disablePast,
  minTime,
  maxTime,
  shouldDisableTime,
  minutesStep,
  ampm,
  ampmInClock,
  slots,
  slotProps,
  readOnly,
  disabled,
  sx,
  autoFocus,
  showViewSwitcher,
  disableIgnoringDatePartForTimeValidation,
  timezone
}) => /*#__PURE__*/_jsx(TimeClock, {
  view: view,
  onViewChange: onViewChange,
  focusedView: focusedView && isTimeView(focusedView) ? focusedView : null,
  onFocusedViewChange: onFocusedViewChange,
  views: views.filter(isTimeView),
  value: value,
  defaultValue: defaultValue,
  referenceDate: referenceDate,
  onChange: onChange,
  className: className,
  classes: classes,
  disableFuture: disableFuture,
  disablePast: disablePast,
  minTime: minTime,
  maxTime: maxTime,
  shouldDisableTime: shouldDisableTime,
  minutesStep: minutesStep,
  ampm: ampm,
  ampmInClock: ampmInClock,
  slots: slots,
  slotProps: slotProps,
  readOnly: readOnly,
  disabled: disabled,
  sx: sx,
  autoFocus: autoFocus,
  showViewSwitcher: showViewSwitcher,
  disableIgnoringDatePartForTimeValidation: disableIgnoringDatePartForTimeValidation,
  timezone: timezone
});
if (process.env.NODE_ENV !== "production") renderTimeViewClock.displayName = "renderTimeViewClock";
export const renderDigitalClockTimeView = ({
  view,
  onViewChange,
  focusedView,
  onFocusedViewChange,
  views,
  value,
  defaultValue,
  referenceDate,
  onChange,
  className,
  classes,
  disableFuture,
  disablePast,
  minTime,
  maxTime,
  shouldDisableTime,
  minutesStep,
  ampm,
  slots,
  slotProps,
  readOnly,
  disabled,
  sx,
  autoFocus,
  disableIgnoringDatePartForTimeValidation,
  timeSteps,
  skipDisabled,
  timezone
}) => /*#__PURE__*/_jsx(DigitalClock, {
  view: view,
  onViewChange: onViewChange,
  focusedView: focusedView && isTimeView(focusedView) ? focusedView : null,
  onFocusedViewChange: onFocusedViewChange,
  views: views.filter(isTimeView),
  value: value,
  defaultValue: defaultValue,
  referenceDate: referenceDate,
  onChange: onChange,
  className: className,
  classes: classes,
  disableFuture: disableFuture,
  disablePast: disablePast,
  minTime: minTime,
  maxTime: maxTime,
  shouldDisableTime: shouldDisableTime,
  minutesStep: minutesStep,
  ampm: ampm,
  slots: slots,
  slotProps: slotProps,
  readOnly: readOnly,
  disabled: disabled,
  sx: sx,
  autoFocus: autoFocus,
  disableIgnoringDatePartForTimeValidation: disableIgnoringDatePartForTimeValidation,
  timeStep: timeSteps?.minutes,
  skipDisabled: skipDisabled,
  timezone: timezone
});
if (process.env.NODE_ENV !== "production") renderDigitalClockTimeView.displayName = "renderDigitalClockTimeView";
export const renderMultiSectionDigitalClockTimeView = ({
  view,
  onViewChange,
  focusedView,
  onFocusedViewChange,
  views,
  value,
  defaultValue,
  referenceDate,
  onChange,
  className,
  classes,
  disableFuture,
  disablePast,
  minTime,
  maxTime,
  shouldDisableTime,
  minutesStep,
  ampm,
  slots,
  slotProps,
  readOnly,
  disabled,
  sx,
  autoFocus,
  disableIgnoringDatePartForTimeValidation,
  timeSteps,
  skipDisabled,
  timezone
}) => /*#__PURE__*/_jsx(MultiSectionDigitalClock, {
  view: view,
  onViewChange: onViewChange,
  focusedView: focusedView && isInternalTimeView(focusedView) ? focusedView : null,
  onFocusedViewChange: onFocusedViewChange,
  views: views.filter(isTimeView),
  value: value,
  defaultValue: defaultValue,
  referenceDate: referenceDate,
  onChange: onChange,
  className: className,
  classes: classes,
  disableFuture: disableFuture,
  disablePast: disablePast,
  minTime: minTime,
  maxTime: maxTime,
  shouldDisableTime: shouldDisableTime,
  minutesStep: minutesStep,
  ampm: ampm,
  slots: slots,
  slotProps: slotProps,
  readOnly: readOnly,
  disabled: disabled,
  sx: sx,
  autoFocus: autoFocus,
  disableIgnoringDatePartForTimeValidation: disableIgnoringDatePartForTimeValidation,
  timeSteps: timeSteps,
  skipDisabled: skipDisabled,
  timezone: timezone
});
if (process.env.NODE_ENV !== "production") renderMultiSectionDigitalClockTimeView.displayName = "renderMultiSectionDigitalClockTimeView";