/**
 * @ignore - internal hook.
 */
export declare function useMultiInputRangeFieldRootProps<TForwardedProps extends {
  [key: string]: any;
}>(forwardedProps: TForwardedProps): UseMultiInputRangeFieldRootPropsReturnValue<TForwardedProps>;
export type UseMultiInputRangeFieldRootPropsReturnValue<TForwardedProps extends {
  [key: string]: any;
}> = Omit<TForwardedProps, 'onBlur'> & {
  onBlur: () => void;
};