import * as React from 'react';
import { TimeRangePickerProps } from "./TimeRangePicker.types.js";
type TimePickerComponent = (<TEnableAccessibleFieldDOMStructure extends boolean = true>(props: TimeRangePickerProps<TEnableAccessibleFieldDOMStructure> & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
};
/**
 * Demos:
 *
 * - [TimeRangePicker](https://mui.com/x/react-date-pickers/time-range-picker/)
 * - [Validation](https://mui.com/x/react-date-pickers/validation/)
 *
 * API:
 *
 * - [TimeRangePicker API](https://mui.com/x/api/date-pickers/time-range-picker/)
 */
declare const TimeRangePicker: TimePickerComponent;
export { TimeRangePicker };