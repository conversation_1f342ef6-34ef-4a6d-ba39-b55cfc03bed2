import * as React from 'react';
import { UseValidationReturnValue } from '@mui/x-date-pickers/validation';
import { PickerValueType } from '@mui/x-date-pickers/models';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PickerManagerEnableAccessibleFieldDOMStructure, PickerManagerError, PickerRangeValue, PickerValue, RangePosition, UseFieldInternalProps } from '@mui/x-date-pickers/internals';
import { PickerAnyRangeManager } from "../../internals/models/managers.js";
import { UseMultiInputFieldSelectedSectionsResponseItem } from "./useMultiInputRangeFieldSelectedSections.js";
import type { UseMultiInputRangeFieldTextFieldProps } from "./useMultiInputRangeField.js";
/**
 * @ignore - internal hook.
 */
export declare function useTextFieldProps<TManager extends PickerAnyRangeManager, TForwardedProps extends UseTextFieldBaseForwardedProps>(parameters: UseTextFieldPropsParameters<TManager, TForwardedProps>): UseMultiInputRangeFieldTextFieldProps<PickerManagerEnableAccessibleFieldDOMStructure<TManager>, TForwardedProps>;
interface UseTextFieldPropsParameters<TManager extends PickerAnyRangeManager, TForwardedProps extends UseTextFieldBaseForwardedProps> {
  valueType: PickerValueType;
  value: PickerRangeValue;
  onChange: FieldChangeHandler<PickerRangeValue, PickerManagerError<TManager>>;
  autoFocus: boolean | undefined;
  forwardedProps: TForwardedProps;
  sharedInternalProps: UseTextFieldSharedInternalProps<TManager>;
  selectedSectionProps: UseMultiInputFieldSelectedSectionsResponseItem;
  position: RangePosition;
  validation: UseValidationReturnValue<PickerRangeValue, PickerManagerError<TManager>>;
}
export interface UseTextFieldBaseForwardedProps {
  onKeyDown?: React.KeyboardEventHandler;
  onClick?: React.MouseEventHandler;
  onFocus?: React.FocusEventHandler;
  [key: string]: any;
}
interface UseTextFieldSharedInternalProps<TManager extends PickerAnyRangeManager> extends Pick<UseFieldInternalProps<PickerValue, PickerManagerEnableAccessibleFieldDOMStructure<TManager>, PickerManagerError<TManager>>, 'enableAccessibleFieldDOMStructure' | 'disabled' | 'readOnly' | 'timezone' | 'format' | 'formatDensity' | 'shouldRespectLeadingZeros'> {}
export {};