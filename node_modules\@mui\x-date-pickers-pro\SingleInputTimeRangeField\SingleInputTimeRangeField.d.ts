import * as React from 'react';
import { SingleInputTimeRangeFieldProps } from "./SingleInputTimeRangeField.types.js";
import { FieldType } from "../models/index.js";
type DateRangeFieldComponent = (<TEnableAccessibleFieldDOMStructure extends boolean = true>(props: SingleInputTimeRangeFieldProps<TEnableAccessibleFieldDOMStructure> & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
  fieldType?: FieldType;
};
/**
 * Demos:
 *
 * - [TimeRangeField](http://mui.com/x/react-date-pickers/time-range-field/)
 * - [Fields](https://mui.com/x/react-date-pickers/fields/)
 *
 * API:
 *
 * - [SingleInputTimeRangeField API](https://mui.com/x/api/single-input-time-range-field/)
 */
declare const SingleInputTimeRangeField: DateRangeFieldComponent;
export { SingleInputTimeRangeField };