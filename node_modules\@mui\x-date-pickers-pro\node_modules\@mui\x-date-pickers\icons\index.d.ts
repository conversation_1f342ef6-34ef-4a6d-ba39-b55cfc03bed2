/**
 * @ignore - internal component.
 */
export declare const ArrowDropDownIcon: import("@mui/material/OverridableComponent").OverridableComponent<import("@mui/material/SvgIcon").SvgIconTypeMap<{}, "svg">> & {
  muiName: string;
};
/**
 * @ignore - internal component.
 */
export declare const ArrowLeftIcon: import("@mui/material/OverridableComponent").OverridableComponent<import("@mui/material/SvgIcon").SvgIconTypeMap<{}, "svg">> & {
  muiName: string;
};
/**
 * @ignore - internal component.
 */
export declare const ArrowRightIcon: import("@mui/material/OverridableComponent").OverridableComponent<import("@mui/material/SvgIcon").SvgIconTypeMap<{}, "svg">> & {
  muiName: string;
};
/**
 * @ignore - internal component.
 */
export declare const CalendarIcon: import("@mui/material/OverridableComponent").OverridableComponent<import("@mui/material/SvgIcon").SvgIconTypeMap<{}, "svg">> & {
  muiName: string;
};
/**
 * @ignore - internal component.
 */
export declare const ClockIcon: import("@mui/material/OverridableComponent").OverridableComponent<import("@mui/material/SvgIcon").SvgIconTypeMap<{}, "svg">> & {
  muiName: string;
};
/**
 * @ignore - internal component.
 */
export declare const DateRangeIcon: import("@mui/material/OverridableComponent").OverridableComponent<import("@mui/material/SvgIcon").SvgIconTypeMap<{}, "svg">> & {
  muiName: string;
};
/**
 * @ignore - internal component.
 */
export declare const TimeIcon: import("@mui/material/OverridableComponent").OverridableComponent<import("@mui/material/SvgIcon").SvgIconTypeMap<{}, "svg">> & {
  muiName: string;
};
/**
 * @ignore - internal component.
 */
export declare const ClearIcon: import("@mui/material/OverridableComponent").OverridableComponent<import("@mui/material/SvgIcon").SvgIconTypeMap<{}, "svg">> & {
  muiName: string;
};