import { UseSingleInputDateTimeRangeFieldProps } from "./SingleInputDateTimeRangeField.types.js";
export declare const useSingleInputDateTimeRangeField: <TEnableAccessibleFieldDOMStructure extends boolean, TProps extends UseSingleInputDateTimeRangeFieldProps<TEnableAccessibleFieldDOMStructure>>(props: TProps) => import("@mui/x-date-pickers/internals").UseFieldReturnValue<TEnableAccessibleFieldDOMStructure, TProps>;