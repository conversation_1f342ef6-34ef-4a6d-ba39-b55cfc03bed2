import { MakeOptional } from '@mui/x-internals/types';
import { UseMobileRangePickerSlots, UseMobileRangePickerSlotProps, MobileRangeOnlyPickerProps } from "../internals/hooks/useMobileRangePicker/index.js";
import { BaseDateRangePickerProps, BaseDateRangePickerSlots, BaseDateRangePickerSlotProps } from "../DateRangePicker/shared.js";
export interface MobileDateRangePickerSlots extends BaseDateRangePickerSlots, MakeOptional<UseMobileRangePickerSlots, 'field'> {}
export interface MobileDateRangePickerSlotProps<TEnableAccessibleFieldDOMStructure extends boolean> extends BaseDateRangePickerSlotProps, Omit<UseMobileRangePickerSlotProps<TEnableAccessibleFieldDOMStructure>, 'tabs' | 'toolbar'> {}
export interface MobileDateRangePickerProps<TEnableAccessibleFieldDOMStructure extends boolean = true> extends BaseDateRangePickerProps, MobileRangeOnlyPickerProps {
  /**
   * Overridable component slots.
   * @default {}
   */
  slots?: MobileDateRangePickerSlots;
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps?: MobileDateRangePickerSlotProps<TEnableAccessibleFieldDOMStructure>;
}