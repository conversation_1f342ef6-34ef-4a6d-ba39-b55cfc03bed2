"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.multiInputDateTimeRangeFieldClasses = exports.getMultiInputDateTimeRangeFieldUtilityClass = void 0;
var _generateUtilityClass = _interopRequireDefault(require("@mui/utils/generateUtilityClass"));
var _generateUtilityClasses = _interopRequireDefault(require("@mui/utils/generateUtilityClasses"));
const multiInputDateTimeRangeFieldClasses = exports.multiInputDateTimeRangeFieldClasses = (0, _generateUtilityClasses.default)('MuiMultiInputDateTimeRangeField', ['root', 'separator']);
const getMultiInputDateTimeRangeFieldUtilityClass = slot => (0, _generateUtilityClass.default)('MuiMultiInputDateTimeRangeField', slot);
exports.getMultiInputDateTimeRangeFieldUtilityClass = getMultiInputDateTimeRangeFieldUtilityClass;