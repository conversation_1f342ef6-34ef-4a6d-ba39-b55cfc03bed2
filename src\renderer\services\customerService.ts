import { 
  Customer, 
  CreateCustomerRequest, 
  UpdateCustomerRequest, 
  CustomerWithCurrentApartment,
  CustomerBookingHistory
} from '../../types/Customer';
import { ApiResponse } from '../../types/common';

export class CustomerService {
  // Create a new customer
  static async createCustomer(customerData: CreateCustomerRequest): Promise<ApiResponse<Customer>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      // Handle file uploads first
      let nidPath: string | undefined;
      let photoPath: string | undefined;

      if (customerData.nid) {
        const nidBuffer = await this.fileToBuffer(customerData.nid);
        const nidResult = await window.electronAPI.files.upload(
          nidBuffer,
          customerData.nid.name,
          'customer',
          0, // Temporary ID, will be updated after customer creation
          'nid'
        );
        nidPath = nidResult.filePath;
      }

      if (customerData.photo) {
        const photoBuffer = await this.fileToBuffer(customerData.photo);
        const photoResult = await window.electronAPI.files.upload(
          photoBuffer,
          customerData.photo.name,
          'customer',
          0, // Temporary ID, will be updated after customer creation
          'photo'
        );
        photoPath = photoResult.filePath;
      }

      // Prepare customer record
      const customerRecord = {
        name: customerData.name,
        address: customerData.address,
        mobile: customerData.mobile,
        occupation: customerData.occupation,
        startDate: customerData.startDate,
        rent: customerData.rent,
        advance: customerData.advance,
        nidPath,
        photoPath,
      };

      const result = await window.electronAPI.customers.create(customerRecord);
      return result;
    } catch (error) {
      console.error('Error creating customer:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create customer',
      };
    }
  }

  // Get all customers
  static async getAllCustomers(): Promise<ApiResponse<CustomerWithCurrentApartment[]>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.customers.getAll();
      return result;
    } catch (error) {
      console.error('Error fetching customers:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch customers',
      };
    }
  }

  // Get customer by ID
  static async getCustomerById(id: number): Promise<ApiResponse<CustomerWithCurrentApartment>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.customers.getById(id);
      return result;
    } catch (error) {
      console.error('Error fetching customer:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch customer',
      };
    }
  }

  // Get available customers (not currently renting)
  static async getAvailableCustomers(): Promise<ApiResponse<Customer[]>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.customers.getAvailable();
      return result;
    } catch (error) {
      console.error('Error fetching available customers:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch available customers',
      };
    }
  }

  // Update customer
  static async updateCustomer(customerData: UpdateCustomerRequest): Promise<ApiResponse<Customer>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      // Handle file uploads if provided
      let nidPath: string | undefined;
      let photoPath: string | undefined;

      if (customerData.nid) {
        const nidBuffer = await this.fileToBuffer(customerData.nid);
        const nidResult = await window.electronAPI.files.upload(
          nidBuffer,
          customerData.nid.name,
          'customer',
          customerData.id,
          'nid'
        );
        nidPath = nidResult.filePath;
      }

      if (customerData.photo) {
        const photoBuffer = await this.fileToBuffer(customerData.photo);
        const photoResult = await window.electronAPI.files.upload(
          photoBuffer,
          customerData.photo.name,
          'customer',
          customerData.id,
          'photo'
        );
        photoPath = photoResult.filePath;
      }

      // Prepare update data
      const updateData = {
        ...customerData,
        ...(nidPath && { nidPath }),
        ...(photoPath && { photoPath }),
      };

      // Remove file objects from update data
      delete updateData.nid;
      delete updateData.photo;

      const result = await window.electronAPI.customers.update(updateData);
      return result;
    } catch (error) {
      console.error('Error updating customer:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update customer',
      };
    }
  }

  // Delete customer
  static async deleteCustomer(id: number): Promise<ApiResponse<void>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.customers.delete(id);
      return result;
    } catch (error) {
      console.error('Error deleting customer:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete customer',
      };
    }
  }

  // Convert File to Buffer for upload
  private static fileToBuffer(file: File): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const arrayBuffer = reader.result as ArrayBuffer;
        const buffer = Buffer.from(arrayBuffer);
        resolve(buffer);
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsArrayBuffer(file);
    });
  }

  // Validate customer data
  static validateCustomerData(data: CreateCustomerRequest | UpdateCustomerRequest): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    // Validate name
    if (!data.name || data.name.trim().length === 0) {
      errors.name = 'Customer name is required';
    } else if (data.name.trim().length > 100) {
      errors.name = 'Customer name must be less than 100 characters';
    }

    // Validate address
    if (!data.address || data.address.trim().length === 0) {
      errors.address = 'Address is required';
    } else if (data.address.trim().length > 200) {
      errors.address = 'Address must be less than 200 characters';
    }

    // Validate mobile
    if (!data.mobile || data.mobile.trim().length === 0) {
      errors.mobile = 'Mobile number is required';
    } else if (!/^[\+]?[1-9][\d]{0,15}$/.test(data.mobile.trim())) {
      errors.mobile = 'Please enter a valid mobile number';
    }

    // Validate occupation
    if (!data.occupation || data.occupation.trim().length === 0) {
      errors.occupation = 'Occupation is required';
    } else if (data.occupation.trim().length > 100) {
      errors.occupation = 'Occupation must be less than 100 characters';
    }

    // Validate start date
    if (!data.startDate) {
      errors.startDate = 'Start date is required';
    }

    // Validate rent
    if (data.rent === undefined || data.rent === null) {
      errors.rent = 'Rent amount is required';
    } else if (data.rent <= 0) {
      errors.rent = 'Rent amount must be greater than 0';
    } else if (data.rent > 1000000) {
      errors.rent = 'Rent amount cannot exceed 1,000,000';
    }

    // Validate advance
    if (data.advance === undefined || data.advance === null) {
      errors.advance = 'Advance amount is required';
    } else if (data.advance < 0) {
      errors.advance = 'Advance amount cannot be negative';
    } else if (data.advance > 1000000) {
      errors.advance = 'Advance amount cannot exceed 1,000,000';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }

  // Calculate customer statistics
  static calculateCustomerStats(customers: CustomerWithCurrentApartment[]): {
    totalCustomers: number;
    activeCustomers: number;
    inactiveCustomers: number;
    totalRentCollection: number;
    averageRent: number;
    occupancyRate: number;
  } {
    const totalCustomers = customers.length;
    const activeCustomers = customers.filter(customer => customer.isActive).length;
    const inactiveCustomers = totalCustomers - activeCustomers;
    const totalRentCollection = customers
      .filter(customer => customer.isActive)
      .reduce((sum, customer) => sum + customer.rent, 0);
    const averageRent = activeCustomers > 0 
      ? totalRentCollection / activeCustomers 
      : 0;
    const occupancyRate = totalCustomers > 0 ? (activeCustomers / totalCustomers) * 100 : 0;

    return {
      totalCustomers,
      activeCustomers,
      inactiveCustomers,
      totalRentCollection,
      averageRent,
      occupancyRate,
    };
  }

  // Sort customers by name
  static sortCustomersByName(customers: CustomerWithCurrentApartment[]): CustomerWithCurrentApartment[] {
    return [...customers].sort((a, b) => a.name.localeCompare(b.name));
  }

  // Filter customers by search term
  static filterCustomers(customers: CustomerWithCurrentApartment[], searchTerm: string): CustomerWithCurrentApartment[] {
    if (!searchTerm.trim()) {
      return customers;
    }

    const term = searchTerm.toLowerCase().trim();
    return customers.filter(customer =>
      customer.name.toLowerCase().includes(term) ||
      customer.mobile.toLowerCase().includes(term) ||
      customer.occupation.toLowerCase().includes(term) ||
      customer.address.toLowerCase().includes(term) ||
      customer.currentApartment?.name.toLowerCase().includes(term)
    );
  }

  // Filter customers by status
  static filterCustomersByStatus(customers: CustomerWithCurrentApartment[], status: 'all' | 'active' | 'inactive'): CustomerWithCurrentApartment[] {
    if (status === 'all') {
      return customers;
    }
    return customers.filter(customer => 
      status === 'active' ? customer.isActive : !customer.isActive
    );
  }

  // Get customer status display text
  static getCustomerStatusText(customer: CustomerWithCurrentApartment): string {
    if (customer.isActive && customer.currentApartment) {
      return `Renting ${customer.currentApartment.name}`;
    } else if (customer.isActive) {
      return 'Active';
    } else {
      return 'Inactive';
    }
  }

  // Format currency for display
  static formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  }

  // Format date for display
  static formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  // Export customers data for reporting
  static exportCustomersData(customers: CustomerWithCurrentApartment[]): any[] {
    return customers.map(customer => ({
      id: customer.id,
      name: customer.name,
      mobile: customer.mobile,
      address: customer.address,
      occupation: customer.occupation,
      startDate: customer.startDate.toISOString(),
      rent: customer.rent,
      advance: customer.advance,
      isActive: customer.isActive,
      currentApartment: customer.currentApartment?.name || 'N/A',
      status: this.getCustomerStatusText(customer),
      createdAt: customer.createdAt.toISOString(),
      updatedAt: customer.updatedAt.toISOString(),
    }));
  }
}
