import * as React from 'react';
import { UseTimeRangeManagerReturnValue } from "../managers/index.js";
import { MultiInputRangeFieldProps } from "../internals/utils/createMultiInputRangeField/index.js";
import { MultiInputTimeRangeFieldClasses } from "./multiInputTimeRangeFieldClasses.js";
export interface MultiInputTimeRangeFieldProps<TEnableAccessibleFieldDOMStructure extends boolean> extends MultiInputRangeFieldProps<UseTimeRangeManagerReturnValue<TEnableAccessibleFieldDOMStructure>> {
  /**
   * Override or extend the styles applied to the component.
   */
  classes?: Partial<MultiInputTimeRangeFieldClasses>;
}
type MultiInputTimeRangeFieldComponent = (<TEnableAccessibleFieldDOMStructure extends boolean = true>(props: MultiInputTimeRangeFieldProps<TEnableAccessibleFieldDOMStructure> & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
};
/**
 * Demos:
 *
 * - [TimeRangeField](http://mui.com/x/react-date-pickers/time-range-field/)
 * - [Fields](https://mui.com/x/react-date-pickers/fields/)
 *
 * API:
 *
 * - [MultiInputTimeRangeField API](https://mui.com/x/api/multi-input-time-range-field/)
 */
declare const MultiInputTimeRangeField: MultiInputTimeRangeFieldComponent;
export { MultiInputTimeRangeField };