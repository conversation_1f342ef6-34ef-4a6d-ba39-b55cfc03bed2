"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getTimeRangePickerToolbarUtilityClass = getTimeRangePickerToolbarUtilityClass;
exports.timeRangePickerToolbarClasses = void 0;
var _generateUtilityClass = _interopRequireDefault(require("@mui/utils/generateUtilityClass"));
var _generateUtilityClasses = _interopRequireDefault(require("@mui/utils/generateUtilityClasses"));
function getTimeRangePickerToolbarUtilityClass(slot) {
  return (0, _generateUtilityClass.default)('MuiTimeRangePickerToolbar', slot);
}
const timeRangePickerToolbarClasses = exports.timeRangePickerToolbarClasses = (0, _generateUtilityClasses.default)('MuiTimeRangePickerToolbar', ['root', 'container', 'separator', 'timeContainer']);