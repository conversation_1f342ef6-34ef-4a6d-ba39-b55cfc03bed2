import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Box,
  Button,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Chip,
  LinearProgress,
  Divider,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Apartment as ApartmentIcon,
  Layers as LayersIcon,
  Home as HomeIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as EmptyCircleIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { FloorWithApartments } from '../../../types/Floor';

interface FloorCardProps {
  floor: FloorWithApartments;
  onEdit: (floor: FloorWithApartments) => void;
  onDelete: (floor: FloorWithApartments) => void;
  onView: (floor: FloorWithApartments) => void;
}

const FloorCard: React.FC<FloorCardProps> = ({
  floor,
  onEdit,
  onDelete,
  onView,
}) => {
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    handleMenuClose();
    onEdit(floor);
  };

  const handleDelete = () => {
    handleMenuClose();
    onDelete(floor);
  };

  const handleView = () => {
    onView(floor);
    navigate(`/floors/${floor.id}/apartments`);
  };

  const handleCardClick = () => {
    handleView();
  };

  const getOccupancyRate = (): number => {
    if (floor.totalApartments === 0) return 0;
    return (floor.occupiedApartments / floor.totalApartments) * 100;
  };

  const getOccupancyColor = (rate: number): 'success' | 'warning' | 'error' => {
    if (rate >= 80) return 'success';
    if (rate >= 50) return 'warning';
    return 'error';
  };

  const getFloorIcon = () => {
    if (floor.floorNumber === 0) {
      return <HomeIcon />;
    }
    return <LayersIcon />;
  };

  const occupancyRate = getOccupancyRate();

  return (
    <>
      <Card
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          cursor: 'pointer',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
          },
        }}
        onClick={handleCardClick}
      >
        <CardContent sx={{ flexGrow: 1, pb: 1 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
            <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
              {getFloorIcon()}
            </Avatar>
            <Box sx={{ flexGrow: 1, minWidth: 0 }}>
              <Typography variant="h6" component="div" noWrap sx={{ fontWeight: 600 }}>
                {floor.name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Floor {floor.floorNumber}
              </Typography>
            </Box>
            <IconButton
              size="small"
              onClick={handleMenuOpen}
              sx={{ ml: 1 }}
            >
              <MoreVertIcon />
            </IconButton>
          </Box>

          {/* Statistics */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Occupancy Rate
              </Typography>
              <Typography variant="body2" fontWeight={600}>
                {occupancyRate.toFixed(1)}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={occupancyRate}
              color={getOccupancyColor(occupancyRate)}
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Apartment Statistics */}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
            <Chip
              icon={<ApartmentIcon />}
              label={`${floor.totalApartments} Total`}
              size="small"
              variant="outlined"
            />
            <Chip
              icon={<CheckCircleIcon />}
              label={`${floor.occupiedApartments} Occupied`}
              size="small"
              color="success"
              variant="outlined"
            />
            <Chip
              icon={<EmptyCircleIcon />}
              label={`${floor.availableApartments} Available`}
              size="small"
              color="default"
              variant="outlined"
            />
          </Box>

          {/* Status Indicator */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="caption" color="text.secondary">
              Status:
            </Typography>
            <Chip
              label={floor.totalApartments === 0 ? 'No Apartments' : 
                    floor.availableApartments === 0 ? 'Fully Occupied' : 
                    floor.occupiedApartments === 0 ? 'All Available' : 'Partially Occupied'}
              size="small"
              color={floor.totalApartments === 0 ? 'default' :
                     floor.availableApartments === 0 ? 'success' :
                     floor.occupiedApartments === 0 ? 'warning' : 'info'}
            />
          </Box>
        </CardContent>

        <CardActions sx={{ pt: 0, px: 2, pb: 2 }}>
          <Button
            size="small"
            startIcon={<ViewIcon />}
            onClick={(e) => {
              e.stopPropagation();
              handleView();
            }}
          >
            View Apartments
          </Button>
          <Button
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              handleEdit();
            }}
          >
            Edit
          </Button>
        </CardActions>
      </Card>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        onClick={(e) => e.stopPropagation()}
      >
        <MenuItem onClick={handleEdit}>
          <EditIcon sx={{ mr: 1 }} fontSize="small" />
          Edit Floor
        </MenuItem>
        <MenuItem onClick={handleView}>
          <ViewIcon sx={{ mr: 1 }} fontSize="small" />
          View Apartments
        </MenuItem>
        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} fontSize="small" />
          Delete Floor
        </MenuItem>
      </Menu>
    </>
  );
};

export default FloorCard;
