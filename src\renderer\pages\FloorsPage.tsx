import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Button,
  Grid,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  InputAdornment,
  Fab,
  Tooltip,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  AutoFixHigh as AutoGenerateIcon,
  Business as BusinessIcon,
  Layers as LayersIcon,
  NavigateNext as NavigateNextIcon,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { FloorWithApartments, CreateFloorRequest, UpdateFloorRequest } from '../../types/Floor';
import { Building } from '../../types/Building';
import { FloorService } from '../services/floorService';
import { BuildingService } from '../services/buildingService';
import { useNotification } from '../contexts/NotificationContext';
import FloorForm from '../components/forms/FloorForm';
import FloorCard from '../components/cards/FloorCard';

const FloorsPage: React.FC = () => {
  const { buildingId } = useParams<{ buildingId: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();

  const [floors, setFloors] = useState<FloorWithApartments[]>([]);
  const [building, setBuilding] = useState<Building | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [formOpen, setFormOpen] = useState(false);
  const [editingFloor, setEditingFloor] = useState<FloorWithApartments | undefined>();
  const [formLoading, setFormLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [floorToDelete, setFloorToDelete] = useState<FloorWithApartments | null>(null);
  const [autoGenerateDialogOpen, setAutoGenerateDialogOpen] = useState(false);
  const [numberOfFloorsToGenerate, setNumberOfFloorsToGenerate] = useState('');

  useEffect(() => {
    if (buildingId) {
      loadBuildingAndFloors();
    }
  }, [buildingId]);

  const loadBuildingAndFloors = async () => {
    try {
      setLoading(true);

      // For now, use mock data since we don't have the backend implemented yet
      const mockBuilding: Building = {
        id: parseInt(buildingId!),
        name: buildingId === '1' ? 'Sunset Apartments' : 'Green Valley Complex',
        address: buildingId === '1' ? '123 Main Street, Downtown' : '789 Pine Street, Suburbs',
        ownerName: buildingId === '1' ? 'John Smith' : 'Sarah Johnson',
        ownerMobileNo: buildingId === '1' ? '******-0123' : '******-0456',
        ownerAddress: buildingId === '1' ? '456 Oak Avenue' : '321 Elm Street',
        rentAmount: buildingId === '1' ? 1200 : 1500,
        advance: buildingId === '1' ? 2400 : 3000,
        numberOfFloor: buildingId === '1' ? 3 : 5,
        agreementDate: new Date('2023-01-15'),
        handOverFromOwner: new Date('2023-02-01'),
        handOverToOwner: undefined,
        conditions: buildingId === '1' ? 'No pets allowed' : 'Parking included',
        comments: buildingId === '1' ? 'Well-maintained building' : 'Modern amenities',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockFloors: FloorWithApartments[] = [
        {
          id: 1,
          name: 'Ground Floor',
          floorNumber: 0,
          buildingId: parseInt(buildingId!),
          createdAt: new Date(),
          updatedAt: new Date(),
          apartments: [],
          totalApartments: 4,
          occupiedApartments: 3,
          availableApartments: 1,
        },
        {
          id: 2,
          name: '1st Floor',
          floorNumber: 1,
          buildingId: parseInt(buildingId!),
          createdAt: new Date(),
          updatedAt: new Date(),
          apartments: [],
          totalApartments: 4,
          occupiedApartments: 4,
          availableApartments: 0,
        },
        {
          id: 3,
          name: '2nd Floor',
          floorNumber: 2,
          buildingId: parseInt(buildingId!),
          createdAt: new Date(),
          updatedAt: new Date(),
          apartments: [],
          totalApartments: 4,
          occupiedApartments: 2,
          availableApartments: 2,
        },
      ];

      setBuilding(mockBuilding);
      setFloors(FloorService.sortFloorsByNumber(mockFloors));
    } catch (error) {
      showError('Failed to load building and floors');
      console.error('Error loading building and floors:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateFloor = () => {
    setEditingFloor(undefined);
    setFormOpen(true);
  };

  const handleEditFloor = (floor: FloorWithApartments) => {
    setEditingFloor(floor);
    setFormOpen(true);
  };

  const handleDeleteFloor = (floor: FloorWithApartments) => {
    setFloorToDelete(floor);
    setDeleteDialogOpen(true);
  };

  const handleViewFloor = (floor: FloorWithApartments) => {
    console.log('Viewing floor:', floor.name);
  };

  const handleFormSubmit = async (data: CreateFloorRequest | UpdateFloorRequest) => {
    try {
      setFormLoading(true);

      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      if ('id' in data) {
        showSuccess('Floor updated successfully');
      } else {
        showSuccess('Floor created successfully');
      }

      setFormOpen(false);
      await loadBuildingAndFloors();
    } catch (error) {
      showError('Failed to save floor');
      console.error('Error saving floor:', error);
    } finally {
      setFormLoading(false);
    }
  };

  const confirmDelete = async () => {
    if (!floorToDelete) return;

    try {
      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      showSuccess('Floor deleted successfully');
      setDeleteDialogOpen(false);
      setFloorToDelete(null);
      await loadBuildingAndFloors();
    } catch (error) {
      showError('Failed to delete floor');
      console.error('Error deleting floor:', error);
    }
  };

  const handleAutoGenerate = async () => {
    try {
      const numberOfFloors = parseInt(numberOfFloorsToGenerate);
      if (isNaN(numberOfFloors) || numberOfFloors <= 0) {
        showError('Please enter a valid number of floors');
        return;
      }

      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      showSuccess(`${numberOfFloors} floors generated successfully`);
      setAutoGenerateDialogOpen(false);
      setNumberOfFloorsToGenerate('');
      await loadBuildingAndFloors();
    } catch (error) {
      showError('Failed to auto-generate floors');
      console.error('Error auto-generating floors:', error);
    }
  };

  const filteredFloors = FloorService.filterFloors(floors, searchTerm);
  const floorStats = FloorService.calculateFloorStats(floors);

  if (!buildingId) {
    return (
      <Container maxWidth="xl">
        <Alert severity="error">Building ID is required</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      {/* Breadcrumbs */}
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs separator={<NavigateNextIcon fontSize="small" />}>
          <Link
            color="inherit"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              navigate('/buildings');
            }}
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Buildings
          </Link>
          <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
            <LayersIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            {building?.name || 'Floors'}
          </Typography>
        </Breadcrumbs>
      </Box>

      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
              Floors Management
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {building ? `Manage floors for ${building.name}` : 'Loading building information...'}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Tooltip title="Auto-generate floors">
              <Button
                variant="outlined"
                startIcon={<AutoGenerateIcon />}
                onClick={() => setAutoGenerateDialogOpen(true)}
                disabled={loading}
              >
                Auto Generate
              </Button>
            </Tooltip>
            <Tooltip title="Refresh">
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={loadBuildingAndFloors}
                disabled={loading}
              >
                Refresh
              </Button>
            </Tooltip>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateFloor}
              size="large"
            >
              Add Floor
            </Button>
          </Box>
        </Box>

        {/* Building Info Card */}
        {building && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {building.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {building.address}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip label={`${floorStats.totalFloors} Floors`} color="primary" />
                  <Chip label={`${floorStats.totalApartments} Apartments`} color="secondary" />
                  <Chip
                    label={`${floorStats.occupancyRate.toFixed(1)}% Occupied`}
                    color={floorStats.occupancyRate >= 80 ? 'success' : floorStats.occupancyRate >= 50 ? 'warning' : 'error'}
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>
        )}

        {/* Search */}
        <TextField
          fullWidth
          placeholder="Search floors..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ maxWidth: 400 }}
        />
      </Box>

      {/* Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      ) : floors.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          No floors found for this building. Click "Add Floor" or "Auto Generate" to create floors.
        </Alert>
      ) : (
        <>
          {/* Statistics */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Floor Statistics
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6} sm={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary" sx={{ fontWeight: 600 }}>
                      {floorStats.totalFloors}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Floors
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="secondary" sx={{ fontWeight: 600 }}>
                      {floorStats.totalApartments}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Apartments
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                      {floorStats.occupiedApartments}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Occupied
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="warning.main" sx={{ fontWeight: 600 }}>
                      {floorStats.availableApartments}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Available
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>

          {/* Floors Grid */}
          <Grid container spacing={3}>
            {filteredFloors.map((floor) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={floor.id}>
                <FloorCard
                  floor={floor}
                  onEdit={handleEditFloor}
                  onDelete={handleDeleteFloor}
                  onView={handleViewFloor}
                />
              </Grid>
            ))}
          </Grid>
        </>
      )}

      {/* Floating Action Button for Mobile */}
      <Fab
        color="primary"
        aria-label="add floor"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          display: { xs: 'flex', md: 'none' },
        }}
        onClick={handleCreateFloor}
      >
        <AddIcon />
      </Fab>

      {/* Floor Form Dialog */}
      <FloorForm
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        floor={editingFloor}
        buildingId={parseInt(buildingId)}
        loading={formLoading}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Floor</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{floorToDelete?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Auto Generate Dialog */}
      <Dialog open={autoGenerateDialogOpen} onClose={() => setAutoGenerateDialogOpen(false)}>
        <DialogTitle>Auto Generate Floors</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            How many floors would you like to generate for {building?.name}?
          </Typography>
          <TextField
            autoFocus
            margin="dense"
            label="Number of Floors"
            type="number"
            fullWidth
            variant="outlined"
            value={numberOfFloorsToGenerate}
            onChange={(e) => setNumberOfFloorsToGenerate(e.target.value)}
            inputProps={{ min: 1, max: 50 }}
            helperText="Floors will be automatically named (Ground Floor, 1st Floor, etc.)"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAutoGenerateDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAutoGenerate} variant="contained">
            Generate Floors
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default FloorsPage;
