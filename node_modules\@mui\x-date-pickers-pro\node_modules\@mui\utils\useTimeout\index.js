"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "Timeout", {
  enumerable: true,
  get: function () {
    return _useTimeout.Timeout;
  }
});
Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () {
    return _useTimeout.default;
  }
});
var _useTimeout = _interopRequireWildcard(require("./useTimeout"));