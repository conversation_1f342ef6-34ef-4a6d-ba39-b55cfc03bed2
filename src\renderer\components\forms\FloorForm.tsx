import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
} from '@mui/material';
import { Floor, CreateFloorRequest, UpdateFloorRequest } from '../../../types/Floor';
import { FloorService } from '../../services/floorService';

interface FloorFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: CreateFloorRequest | UpdateFloorRequest) => Promise<void>;
  floor?: Floor; // For edit mode
  buildingId: number; // Required for creating new floors
  loading?: boolean;
}

interface FormData {
  name: string;
  floorNumber: string;
}

const FloorForm: React.FC<FloorFormProps> = ({
  open,
  onClose,
  onSubmit,
  floor,
  buildingId,
  loading = false,
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    floorNumber: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditMode = !!floor;

  // Initialize form data when floor prop changes
  useEffect(() => {
    if (floor) {
      setFormData({
        name: floor.name,
        floorNumber: floor.floorNumber.toString(),
      });
    } else {
      setFormData({
        name: '',
        floorNumber: '',
      });
    }
    setErrors({});
  }, [floor, open]);

  // Auto-generate floor name when floor number changes (for create mode)
  useEffect(() => {
    if (!isEditMode && formData.floorNumber) {
      const floorNum = parseInt(formData.floorNumber);
      if (!isNaN(floorNum)) {
        const generatedName = FloorService.generateFloorName(floorNum);
        setFormData(prev => ({ ...prev, name: generatedName }));
      }
    }
  }, [formData.floorNumber, isEditMode]);

  const handleInputChange = (field: keyof FormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const value = event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleFloorNumberChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    // Only allow positive integers
    if (value === '' || /^\d+$/.test(value)) {
      setFormData(prev => ({ ...prev, floorNumber: value }));
      
      // Clear error when user starts typing
      if (errors.floorNumber) {
        setErrors(prev => ({ ...prev, floorNumber: '' }));
      }
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate name
    if (!formData.name.trim()) {
      newErrors.name = 'Floor name is required';
    } else if (formData.name.trim().length > 100) {
      newErrors.name = 'Floor name must be less than 100 characters';
    }

    // Validate floor number (only for create mode)
    if (!isEditMode) {
      if (!formData.floorNumber.trim()) {
        newErrors.floorNumber = 'Floor number is required';
      } else {
        const floorNum = parseInt(formData.floorNumber);
        if (isNaN(floorNum)) {
          newErrors.floorNumber = 'Floor number must be a valid number';
        } else if (floorNum < 0) {
          newErrors.floorNumber = 'Floor number cannot be negative';
        } else if (floorNum > 50) {
          newErrors.floorNumber = 'Floor number cannot exceed 50';
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const submitData = isEditMode
        ? {
            id: floor!.id,
            name: formData.name.trim(),
          } as UpdateFloorRequest
        : {
            name: formData.name.trim(),
            floorNumber: parseInt(formData.floorNumber),
            buildingId,
          } as CreateFloorRequest;

      await onSubmit(submitData);
    } catch (error) {
      console.error('Error submitting floor form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      floorNumber: '',
    });
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  const getFloorNumberOptions = () => {
    const options = [];
    for (let i = 0; i <= 20; i++) {
      options.push(
        <MenuItem key={i} value={i.toString()}>
          {i} - {FloorService.generateFloorName(i)}
        </MenuItem>
      );
    }
    return options;
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
          {isEditMode ? 'Edit Floor' : 'Create New Floor'}
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <Grid container spacing={3}>
            {/* Floor Number - Only show in create mode */}
            {!isEditMode && (
              <Grid item xs={12}>
                <FormControl fullWidth error={!!errors.floorNumber}>
                  <InputLabel>Floor Number</InputLabel>
                  <Select
                    value={formData.floorNumber}
                    label="Floor Number"
                    onChange={(e) => setFormData(prev => ({ ...prev, floorNumber: e.target.value }))}
                  >
                    {getFloorNumberOptions()}
                  </Select>
                  {errors.floorNumber && (
                    <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                      {errors.floorNumber}
                    </Typography>
                  )}
                </FormControl>
              </Grid>
            )}

            {/* Floor Name */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Floor Name"
                value={formData.name}
                onChange={handleInputChange('name')}
                error={!!errors.name}
                helperText={errors.name}
                placeholder="e.g., Ground Floor, 1st Floor"
              />
            </Grid>

            {/* Info Alert */}
            <Grid item xs={12}>
              <Alert severity="info" sx={{ mt: 1 }}>
                {isEditMode
                  ? 'You can only edit the floor name. Floor number cannot be changed.'
                  : 'The floor name will be automatically generated based on the floor number, but you can customize it.'}
              </Alert>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={isSubmitting || loading}
          startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
        >
          {isSubmitting ? 'Saving...' : isEditMode ? 'Update Floor' : 'Create Floor'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FloorForm;
