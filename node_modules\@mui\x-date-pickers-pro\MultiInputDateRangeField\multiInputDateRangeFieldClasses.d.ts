import { MultiInputRangeFieldClasses } from "../internals/utils/createMultiInputRangeField/index.js";
export interface MultiInputDateRangeFieldClasses extends MultiInputRangeFieldClasses {}
export type MultiInputDateRangeFieldClassKey = keyof MultiInputRangeFieldClasses;
export declare const multiInputDateRangeFieldClasses: MultiInputRangeFieldClasses;
export declare const getMultiInputDateRangeFieldUtilityClass: (slot: string) => string;