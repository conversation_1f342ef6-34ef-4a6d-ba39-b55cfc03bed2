"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useTimeRangePickerDefaultizedProps = useTimeRangePickerDefaultizedProps;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _styles = require("@mui/material/styles");
var _internals = require("@mui/x-date-pickers/internals");
var _hooks = require("@mui/x-date-pickers/hooks");
var _TimeRangePickerToolbar = require("./TimeRangePickerToolbar");
var _TimeRangePickerTabs = require("./TimeRangePickerTabs");
function useTimeRangePickerDefaultizedProps(props, name) {
  const adapter = (0, _hooks.usePickerAdapter)();
  const themeProps = (0, _styles.useThemeProps)({
    props,
    name
  });
  const validationProps = (0, _internals.useApplyDefaultValuesToTimeValidationProps)(themeProps);
  const ampm = themeProps.ampm ?? adapter.is12HourCycleInCurrentLocale();
  const {
    openTo,
    views: defaultViews
  } = (0, _internals.applyDefaultViewProps)({
    views: themeProps.views,
    openTo: themeProps.openTo,
    defaultViews: ['hours', 'minutes'],
    defaultOpenTo: 'hours'
  });
  const localeText = React.useMemo(() => {
    if (themeProps.localeText?.toolbarTitle == null) {
      return themeProps.localeText;
    }
    return (0, _extends2.default)({}, themeProps.localeText, {
      timeRangePickerToolbarTitle: themeProps.localeText.toolbarTitle
    });
  }, [themeProps.localeText]);
  const {
    shouldRenderTimeInASingleColumn,
    thresholdToRenderTimeInASingleColumn,
    views,
    timeSteps
  } = (0, _internals.resolveTimeViewsResponse)({
    thresholdToRenderTimeInASingleColumn: themeProps.thresholdToRenderTimeInASingleColumn,
    ampm,
    timeSteps: themeProps.timeSteps,
    views: defaultViews
  });
  return (0, _extends2.default)({}, themeProps, validationProps, {
    localeText,
    timeSteps,
    openTo,
    shouldRenderTimeInASingleColumn,
    thresholdToRenderTimeInASingleColumn,
    views,
    ampm,
    slots: (0, _extends2.default)({
      tabs: _TimeRangePickerTabs.TimeRangePickerTabs,
      toolbar: _TimeRangePickerToolbar.TimeRangePickerToolbar
    }, themeProps.slots),
    slotProps: (0, _extends2.default)({}, themeProps.slotProps, {
      toolbar: (0, _extends2.default)({
        ampm
      }, themeProps.slotProps?.toolbar)
    })
  });
}