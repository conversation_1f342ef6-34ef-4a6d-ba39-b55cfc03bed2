import * as React from 'react';
import { BaseToolbarProps, ExportedBaseToolbarProps } from '@mui/x-date-pickers/internals';
import { TimeRangePickerToolbarClasses } from "./timeRangePickerToolbarClasses.js";
export interface TimeRangePickerToolbarProps extends Omit<BaseToolbarProps, 'toolbarFormat'>, ExportedTimeRangePickerToolbarProps {
  ampm: boolean;
}
export interface ExportedTimeRangePickerToolbarProps extends Omit<ExportedBaseToolbarProps, 'toolbarFormat'> {
  /**
   * Override or extend the styles applied to the component.
   */
  classes?: Partial<TimeRangePickerToolbarClasses>;
}
declare const TimeRangePickerToolbar: React.ForwardRefExoticComponent<TimeRangePickerToolbarProps & React.RefAttributes<HTMLDivElement>>;
export { TimeRangePickerToolbar };