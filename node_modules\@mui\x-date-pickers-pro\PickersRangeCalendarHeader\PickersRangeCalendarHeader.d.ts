import * as React from 'react';
import { PickersRangeCalendarHeaderProps } from "./PickersRangeCalendarHeader.types.js";
type PickersRangeCalendarHeaderComponent = ((props: PickersRangeCalendarHeaderProps & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
};
declare const PickersRangeCalendarHeader: PickersRangeCalendarHeaderComponent;
export { PickersRangeCalendarHeader };