import { DefaultizedProps } from '@mui/x-internals/types';
import { PickerSelectionState, PickerViewRenderer, TimeViewWithMeridiem, BaseClockProps, PickerRangeValue } from '@mui/x-date-pickers/internals';
export type TimeRangePickerTimeWrapperProps<TComponentProps extends DefaultizedProps<Omit<BaseClockProps<TimeViewWithMeridiem>, 'value' | 'defaultValue' | 'onChange'>, 'views'>> = Omit<TComponentProps, 'views' | 'view' | 'onViewChange' | 'value' | 'defaultValue' | 'onChange'> & {
  view: TimeViewWithMeridiem;
  onViewChange?: (view: TimeViewWithMeridiem) => void;
  views: readonly TimeViewWithMeridiem[];
  value?: PickerRangeValue;
  defaultValue?: PickerRangeValue;
  onChange?: (value: PickerRangeValue, selectionState: PickerSelectionState, selectedView: TimeViewWithMeridiem) => void;
  viewRenderer: PickerViewRenderer<PickerRangeValue, TComponentProps> | null;
  openTo?: TimeViewWithMeridiem;
};
/**
 * @ignore - internal component.
 */
declare function TimeRangePickerTimeWrapper<TComponentProps extends DefaultizedProps<Omit<BaseClockProps<TimeViewWithMeridiem>, 'value' | 'defaultValue' | 'onChange'>, 'views'>>(props: TimeRangePickerTimeWrapperProps<TComponentProps>): import("react").ReactNode;
export { TimeRangePickerTimeWrapper };