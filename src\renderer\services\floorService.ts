import { Floor, CreateFloorRequest, UpdateFloorRequest, FloorWithApartments } from '../../types/Floor';
import { ApiResponse } from '../../types/common';

export class FloorService {
  // Create a new floor
  static async createFloor(floorData: CreateFloorRequest): Promise<ApiResponse<Floor>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.floors.create(floorData);
      return result;
    } catch (error) {
      console.error('Error creating floor:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create floor',
      };
    }
  }

  // Get all floors for a building
  static async getFloorsByBuilding(buildingId: number): Promise<ApiResponse<FloorWithApartments[]>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.floors.getByBuilding(buildingId);
      return result;
    } catch (error) {
      console.error('Error fetching floors:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch floors',
      };
    }
  }

  // Update floor
  static async updateFloor(floorData: UpdateFloorRequest): Promise<ApiResponse<Floor>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.floors.update(floorData);
      return result;
    } catch (error) {
      console.error('Error updating floor:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update floor',
      };
    }
  }

  // Delete floor
  static async deleteFloor(id: number): Promise<ApiResponse<void>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.floors.delete(id);
      return result;
    } catch (error) {
      console.error('Error deleting floor:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete floor',
      };
    }
  }

  // Auto-generate floors for a building
  static async autoGenerateFloors(buildingId: number, numberOfFloors: number): Promise<ApiResponse<Floor[]>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.floors.autoGenerate(buildingId, numberOfFloors);
      return result;
    } catch (error) {
      console.error('Error auto-generating floors:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to auto-generate floors',
      };
    }
  }

  // Generate floor name based on floor number
  static generateFloorName(floorNumber: number): string {
    if (floorNumber === 0) {
      return 'Ground Floor';
    } else if (floorNumber === 1) {
      return '1st Floor';
    } else if (floorNumber === 2) {
      return '2nd Floor';
    } else if (floorNumber === 3) {
      return '3rd Floor';
    } else {
      return `${floorNumber}th Floor`;
    }
  }

  // Validate floor data
  static validateFloorData(data: CreateFloorRequest | UpdateFloorRequest): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    // Validate name
    if (!data.name || data.name.trim().length === 0) {
      errors.name = 'Floor name is required';
    } else if (data.name.trim().length > 100) {
      errors.name = 'Floor name must be less than 100 characters';
    }

    // Validate floor number for create requests
    if ('floorNumber' in data) {
      if (data.floorNumber < 0) {
        errors.floorNumber = 'Floor number cannot be negative';
      } else if (data.floorNumber > 50) {
        errors.floorNumber = 'Floor number cannot exceed 50';
      }
    }

    // Validate building ID for create requests
    if ('buildingId' in data) {
      if (!data.buildingId || data.buildingId <= 0) {
        errors.buildingId = 'Valid building ID is required';
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }

  // Get floor statistics
  static calculateFloorStats(floors: FloorWithApartments[]): {
    totalFloors: number;
    totalApartments: number;
    occupiedApartments: number;
    availableApartments: number;
    occupancyRate: number;
  } {
    const totalFloors = floors.length;
    const totalApartments = floors.reduce((sum, floor) => sum + floor.totalApartments, 0);
    const occupiedApartments = floors.reduce((sum, floor) => sum + floor.occupiedApartments, 0);
    const availableApartments = floors.reduce((sum, floor) => sum + floor.availableApartments, 0);
    const occupancyRate = totalApartments > 0 ? (occupiedApartments / totalApartments) * 100 : 0;

    return {
      totalFloors,
      totalApartments,
      occupiedApartments,
      availableApartments,
      occupancyRate,
    };
  }

  // Sort floors by floor number
  static sortFloorsByNumber(floors: Floor[]): Floor[] {
    return [...floors].sort((a, b) => a.floorNumber - b.floorNumber);
  }

  // Filter floors by search term
  static filterFloors(floors: FloorWithApartments[], searchTerm: string): FloorWithApartments[] {
    if (!searchTerm.trim()) {
      return floors;
    }

    const term = searchTerm.toLowerCase().trim();
    return floors.filter(floor =>
      floor.name.toLowerCase().includes(term) ||
      floor.floorNumber.toString().includes(term)
    );
  }

  // Generate multiple floors with proper naming
  static generateFloorList(numberOfFloors: number, buildingId: number): CreateFloorRequest[] {
    const floors: CreateFloorRequest[] = [];

    for (let i = 0; i < numberOfFloors; i++) {
      floors.push({
        name: this.generateFloorName(i),
        floorNumber: i,
        buildingId,
      });
    }

    return floors;
  }

  // Validate floor number uniqueness within a building
  static validateFloorNumberUniqueness(
    floorNumber: number,
    buildingId: number,
    existingFloors: Floor[],
    excludeFloorId?: number
  ): boolean {
    return !existingFloors.some(floor =>
      floor.floorNumber === floorNumber &&
      floor.buildingId === buildingId &&
      floor.id !== excludeFloorId
    );
  }

  // Get next available floor number
  static getNextAvailableFloorNumber(existingFloors: Floor[]): number {
    if (existingFloors.length === 0) return 0;

    const sortedFloors = this.sortFloorsByNumber(existingFloors);
    const lastFloor = sortedFloors[sortedFloors.length - 1];
    return lastFloor.floorNumber + 1;
  }

  // Check if floors can be auto-generated (no conflicts)
  static canAutoGenerateFloors(
    numberOfFloors: number,
    existingFloors: Floor[]
  ): { canGenerate: boolean; conflicts: number[] } {
    const conflicts: number[] = [];

    for (let i = 0; i < numberOfFloors; i++) {
      if (existingFloors.some(floor => floor.floorNumber === i)) {
        conflicts.push(i);
      }
    }

    return {
      canGenerate: conflicts.length === 0,
      conflicts,
    };
  }

  // Get floor summary for display
  static getFloorSummary(floor: FloorWithApartments): string {
    const occupancyRate = floor.totalApartments > 0
      ? ((floor.occupiedApartments / floor.totalApartments) * 100).toFixed(1)
      : '0';

    return `${floor.name} - ${floor.totalApartments} apartments (${occupancyRate}% occupied)`;
  }

  // Export floors data for reporting
  static exportFloorsData(floors: FloorWithApartments[]): any[] {
    return floors.map(floor => ({
      id: floor.id,
      name: floor.name,
      floorNumber: floor.floorNumber,
      buildingId: floor.buildingId,
      totalApartments: floor.totalApartments,
      occupiedApartments: floor.occupiedApartments,
      availableApartments: floor.availableApartments,
      occupancyRate: floor.totalApartments > 0
        ? ((floor.occupiedApartments / floor.totalApartments) * 100).toFixed(2)
        : '0.00',
      createdAt: floor.createdAt.toISOString(),
      updatedAt: floor.updatedAt.toISOString(),
    }));
  }
}
