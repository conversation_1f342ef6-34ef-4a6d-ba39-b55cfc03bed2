"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getTimeRangePickerTabsUtilityClass = getTimeRangePickerTabsUtilityClass;
exports.timeRangePickerTabsClasses = void 0;
var _generateUtilityClass = _interopRequireDefault(require("@mui/utils/generateUtilityClass"));
var _generateUtilityClasses = _interopRequireDefault(require("@mui/utils/generateUtilityClasses"));
function getTimeRangePickerTabsUtilityClass(slot) {
  return (0, _generateUtilityClass.default)('MuiTimeRangePickerTabs', slot);
}
const timeRangePickerTabsClasses = exports.timeRangePickerTabsClasses = (0, _generateUtilityClasses.default)('MuiTimeRangePickerTabs', ['root', 'tab']);