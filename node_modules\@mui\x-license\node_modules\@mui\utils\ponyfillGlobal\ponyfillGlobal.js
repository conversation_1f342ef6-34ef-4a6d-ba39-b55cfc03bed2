"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
/* eslint-disable */
/**
 * https://github.com/zloirock/core-js/issues/86#issuecomment-115759028
 * @deprecated Use `globalThis` instead.
 */
var _default = exports.default = typeof window != 'undefined' && window.Math == Math ? window : typeof self != 'undefined' && self.Math == Math ? self : Function('return this')();