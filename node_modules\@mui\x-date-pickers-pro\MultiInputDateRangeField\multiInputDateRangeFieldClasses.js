"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.multiInputDateRangeFieldClasses = exports.getMultiInputDateRangeFieldUtilityClass = void 0;
var _generateUtilityClass = _interopRequireDefault(require("@mui/utils/generateUtilityClass"));
var _generateUtilityClasses = _interopRequireDefault(require("@mui/utils/generateUtilityClasses"));
const multiInputDateRangeFieldClasses = exports.multiInputDateRangeFieldClasses = (0, _generateUtilityClasses.default)('MuiMultiInputDateRangeField', ['root', 'separator']);
const getMultiInputDateRangeFieldUtilityClass = slot => (0, _generateUtilityClass.default)('MuiMultiInputDateRangeField', slot);
exports.getMultiInputDateRangeFieldUtilityClass = getMultiInputDateRangeFieldUtilityClass;