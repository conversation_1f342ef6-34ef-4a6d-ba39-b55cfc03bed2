"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TimeRangePickerTimeWrapper = TimeRangePickerTimeWrapper;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _hooks = require("@mui/x-date-pickers/hooks");
var _dateUtils = require("../internals/utils/date-utils");
var _dateRangeManager = require("../internals/utils/date-range-manager");
var _hooks2 = require("../hooks");
const _excluded = ["viewRenderer", "value", "onChange", "defaultValue", "onViewChange", "views", "className", "referenceDate"];
/**
 * @ignore - internal component.
 */
function TimeRangePickerTimeWrapper(props) {
  const adapter = (0, _hooks.usePickerAdapter)();
  const {
      viewRenderer,
      value,
      onChange,
      defaultValue,
      onViewChange,
      views,
      referenceDate: referenceDateProp
    } = props,
    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  const {
    rangePosition
  } = (0, _hooks2.usePickerRangePositionContext)();
  if (!viewRenderer) {
    return null;
  }
  const currentValue = (rangePosition === 'start' ? value?.[0] : value?.[1]) ?? null;
  const currentDefaultValue = (rangePosition === 'start' ? defaultValue?.[0] : defaultValue?.[1]) ?? null;
  const referenceDate = (0, _dateRangeManager.resolveReferenceDate)(referenceDateProp, rangePosition);
  const handleOnChange = (newDate, selectionState, selectedView) => {
    if (!onChange || !value) {
      return;
    }
    const {
      newRange
    } = (0, _dateRangeManager.calculateRangeChange)({
      newDate,
      adapter,
      range: value,
      rangePosition
    });
    const isFullRangeSelected = rangePosition === 'end' && (0, _dateUtils.isRangeValid)(adapter, newRange);
    onChange(newRange, isFullRangeSelected ? 'finish' : 'partial', selectedView);
  };
  return viewRenderer((0, _extends2.default)({}, other, {
    referenceDate,
    views,
    onViewChange,
    value: currentValue,
    onChange: handleOnChange,
    defaultValue: currentDefaultValue
  }));
}