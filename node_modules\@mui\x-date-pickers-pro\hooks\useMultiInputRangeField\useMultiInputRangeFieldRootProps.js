"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useMultiInputRangeFieldRootProps = useMultiInputRangeFieldRootProps;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _useEventCallback = _interopRequireDefault(require("@mui/utils/useEventCallback"));
var _internals = require("@mui/x-date-pickers/internals");
/**
 * @ignore - internal hook.
 */
function useMultiInputRangeFieldRootProps(forwardedProps) {
  const pickerContext = (0, _internals.useNullablePickerContext)();
  const privatePickerContext = (0, _internals.usePickerPrivateContext)();
  const handleBlur = (0, _useEventCallback.default)(() => {
    if (!pickerContext || privatePickerContext.viewContainerRole !== 'tooltip') {
      return;
    }
    (0, _internals.executeInTheNextEventLoopTick)(() => {
      if (privatePickerContext.rootRefObject.current?.contains((0, _internals.getActiveElement)(privatePickerContext.rootRefObject.current)) || pickerContext.popupRef.current?.contains((0, _internals.getActiveElement)(pickerContext.popupRef.current))) {
        return;
      }
      privatePickerContext.dismissViews();
    });
  });
  return (0, _extends2.default)({}, forwardedProps, {
    onBlur: handleBlur
  });
}