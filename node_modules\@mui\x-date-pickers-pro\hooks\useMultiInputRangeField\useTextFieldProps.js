"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useTextFieldProps = useTextFieldProps;
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _useEventCallback = _interopRequireDefault(require("@mui/utils/useEventCallback"));
var _managers = require("@mui/x-date-pickers/managers");
var _internals = require("@mui/x-date-pickers/internals");
var _useNullablePickerRangePositionContext = require("../../internals/hooks/useNullablePickerRangePositionContext");
const _excluded = ["clearable", "onClear", "openPickerAriaLabel"];
/**
 * @ignore - internal hook.
 */
function useTextFieldProps(parameters) {
  const pickerContext = (0, _internals.useNullablePickerContext)();
  const fieldPrivateContext = (0, _internals.useNullableFieldPrivateContext)();
  const pickerPrivateContext = (0, _internals.usePickerPrivateContext)();
  const rangePositionContext = (0, _useNullablePickerRangePositionContext.useNullablePickerRangePositionContext)();
  const rangePosition = rangePositionContext?.rangePosition ?? 'start';
  const setRangePosition = rangePositionContext?.setRangePosition;
  const previousRangePosition = React.useRef(rangePosition);
  const {
    forwardedProps,
    sharedInternalProps,
    selectedSectionProps,
    valueType,
    position,
    value,
    onChange,
    autoFocus,
    validation
  } = parameters;
  let useManager;
  switch (valueType) {
    case 'date':
      {
        useManager = _managers.useDateManager;
        break;
      }
    case 'time':
      {
        useManager = _managers.useTimeManager;
        break;
      }
    case 'date-time':
      {
        useManager = _managers.useDateTimeManager;
        break;
      }
    default:
      {
        throw new Error(`Unknown valueType: ${valueType}`);
      }
  }
  const manager = useManager({
    enableAccessibleFieldDOMStructure: sharedInternalProps.enableAccessibleFieldDOMStructure
  });
  const openPickerIfPossible = event => {
    if (!pickerContext) {
      return;
    }
    setRangePosition?.(position);
    if (pickerContext.triggerStatus === 'enabled') {
      event.preventDefault();
      pickerContext.setOpen(true);
    }
  };
  const handleKeyDown = (0, _useEventCallback.default)(event => {
    if (event.key === 'Enter' || event.key === ' ') {
      openPickerIfPossible(event);
    }
  });

  // Registering `onClick` listener on the root element as well to correctly handle cases where user is clicking on `label`
  // which has `pointer-events: none` and due to DOM structure the `input` does not catch the click event
  const handleClick = (0, _useEventCallback.default)(event => {
    openPickerIfPossible(event);
  });
  const handleFocus = (0, _useEventCallback.default)(event => {
    forwardedProps.onFocus?.(event);
    if (pickerContext?.open) {
      setRangePosition?.(position);
      if (previousRangePosition.current !== position && pickerContext.initialView) {
        pickerContext.setView?.(pickerContext.initialView);
      }
    }
  });
  const handleChange = (0, _useEventCallback.default)((newSingleValue, rawContext) => {
    const newRange = position === 'start' ? [newSingleValue, value[1]] : [value[0], newSingleValue];
    const context = (0, _extends2.default)({}, rawContext, {
      validationError: validation.getValidationErrorForNewValue(newRange)
    });
    onChange(newRange, context);
  });
  const allProps = (0, _extends2.default)({
    value: position === 'start' ? value[0] : value[1],
    error: position === 'start' ? !!validation.validationError[0] : !!validation.validationError[1],
    id: `${pickerPrivateContext.labelId}-${position}`,
    autoFocus: position === 'start' ? autoFocus : undefined
  }, forwardedProps, sharedInternalProps, selectedSectionProps, {
    onClick: handleClick,
    onFocus: handleFocus,
    onKeyDown: handleKeyDown,
    onChange: handleChange
  });
  const _ref = (0, _internals.useField)({
      manager,
      props: allProps,
      skipContextFieldRefAssignment: rangePosition !== position
    }),
    fieldResponse = (0, _objectWithoutPropertiesLoose2.default)(_ref, _excluded);
  React.useEffect(() => {
    if (!pickerContext?.open || pickerContext?.variant === 'mobile') {
      return;
    }
    fieldPrivateContext?.fieldRef.current?.focusField();
    if (!fieldPrivateContext?.fieldRef.current || pickerContext.view === pickerContext.initialView) {
      // could happen when the user is switching between the inputs
      previousRangePosition.current = rangePosition;
      return;
    }

    // bring back focus to the field
    // currentView is present on DateTimeRangePicker
    fieldPrivateContext?.fieldRef.current.setSelectedSections(
    // use the current view or `0` when the range position has just been swapped
    previousRangePosition.current === rangePosition ? pickerContext.view : 0);
    previousRangePosition.current = rangePosition;
  }, [rangePosition, pickerContext?.open, pickerContext?.variant, pickerContext?.initialView, pickerContext?.view, fieldPrivateContext?.fieldRef]);
  return fieldResponse;
}