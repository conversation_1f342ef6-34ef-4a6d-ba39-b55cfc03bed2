import { ExportedPickerFieldUIProps, PickerFieldUISlots, PickerFieldUISlotProps } from '@mui/x-date-pickers/internals';
import { BuiltInFieldTextFieldProps } from '@mui/x-date-pickers/models';
import { DateTimeRangeManagerFieldInternalProps } from "../managers/useDateTimeRangeManager.js";
export interface UseSingleInputDateTimeRangeFieldProps<TEnableAccessibleFieldDOMStructure extends boolean> extends DateTimeRangeManagerFieldInternalProps<TEnableAccessibleFieldDOMStructure>, Omit<ExportedPickerFieldUIProps, 'openPickerButtonPosition'> {}
export type SingleInputDateTimeRangeFieldProps<TEnableAccessibleFieldDOMStructure extends boolean = true> = Omit<BuiltInFieldTextFieldProps<TEnableAccessibleFieldDOMStructure>, keyof UseSingleInputDateTimeRangeFieldProps<TEnableAccessibleFieldDOMStructure>> & UseSingleInputDateTimeRangeFieldProps<TEnableAccessibleFieldDOMStructure> & {
  /**
   * Overridable component slots.
   * @default {}
   */
  slots?: SingleInputDateTimeRangeFieldSlots;
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps?: SingleInputDateTimeRangeFieldSlotProps;
};
export interface SingleInputDateTimeRangeFieldSlots extends PickerFieldUISlots {}
export interface SingleInputDateTimeRangeFieldSlotProps extends PickerFieldUISlotProps {}