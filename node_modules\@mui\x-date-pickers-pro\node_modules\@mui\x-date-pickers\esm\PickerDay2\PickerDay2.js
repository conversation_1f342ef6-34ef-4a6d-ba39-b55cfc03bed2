import _objectWithoutPropertiesLoose from "@babel/runtime/helpers/esm/objectWithoutPropertiesLoose";
import _extends from "@babel/runtime/helpers/esm/extends";
const _excluded = ["autoFocus", "className", "classes", "hidden", "isAnimating", "onClick", "onDaySelect", "onFocus", "onBlur", "onKeyDown", "onMouseDown", "onMouseEnter", "children", "isFirstVisibleCell", "isLastVisibleCell", "day", "selected", "disabled", "today", "outsideCurrentMonth", "disableMargin", "disableHighlightToday", "showDaysOutsideCurrentMonth", "isVisuallySelected"];
import * as React from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import { alpha, styled, useThemeProps } from '@mui/material/styles';
import ButtonBase from '@mui/material/ButtonBase';
import useForkRef from '@mui/utils/useForkRef';
import composeClasses from '@mui/utils/composeClasses';
import useEnhancedEffect from '@mui/utils/useEnhancedEffect';
import { DAY_MARGIN, DAY_SIZE } from "../internals/constants/dimensions.js";
import { pickerDay2Classes, getPickerDay2UtilityClass } from "./pickerDay2Classes.js";
import { usePickerAdapter } from "../hooks/usePickerAdapter.js";
import { usePickerDayOwnerState } from "../PickersDay/usePickerDayOwnerState.js";
import { jsx as _jsx } from "react/jsx-runtime";
const useUtilityClasses = (ownerState, classes) => {
  const {
    isDaySelected,
    disableHighlightToday,
    isDayCurrent,
    isDayDisabled,
    isDayOutsideMonth,
    isDayFillerCell
  } = ownerState;
  const slots = {
    root: ['root', isDaySelected && !isDayFillerCell && 'selected', isDayDisabled && 'disabled', !disableHighlightToday && isDayCurrent && !isDaySelected && !isDayFillerCell && 'today', isDayOutsideMonth && 'dayOutsideMonth', isDayFillerCell && 'fillerCell']
  };
  return composeClasses(slots, getPickerDay2UtilityClass, classes);
};
const PickerDay2Root = styled(ButtonBase, {
  name: 'MuiPickerDay2',
  slot: 'Root',
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.root, !ownerState.disableHighlightToday && ownerState.isDayCurrent && styles.today, !ownerState.isDayOutsideMonth && styles.dayOutsideMonth, ownerState.isDayFillerCell && styles.fillerCell];
  }
})(({
  theme
}) => _extends({
  '--PickerDay-horizontalMargin': `${DAY_MARGIN}px`,
  '--PickerDay-size': `${DAY_SIZE}px`
}, theme.typography.caption, {
  width: 'var(--PickerDay-size)',
  height: 'var(--PickerDay-size)',
  borderRadius: 'calc(var(--PickerDay-size) / 2)',
  padding: 0,
  // explicitly setting to `transparent` to avoid potentially getting impacted by change from the overridden component
  backgroundColor: 'transparent',
  transition: theme.transitions.create('background-color', {
    duration: theme.transitions.duration.short
  }),
  color: (theme.vars || theme).palette.text.primary,
  '@media (pointer: fine)': {
    '&:hover': {
      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.hoverOpacity)
    }
  },
  '&:focus': {
    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.focusOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.focusOpacity)
  },
  marginLeft: 'var(--PickerDay-horizontalMargin)',
  marginRight: 'var(--PickerDay-horizontalMargin)',
  variants: [{
    props: {
      isDaySelected: true
    },
    style: {
      color: (theme.vars || theme).palette.primary.contrastText,
      backgroundColor: (theme.vars || theme).palette.primary.main,
      fontWeight: theme.typography.fontWeightMedium,
      '&:focus, &:hover': {
        willChange: 'background-color',
        backgroundColor: (theme.vars || theme).palette.primary.dark
      },
      [`&.${pickerDay2Classes.disabled}`]: {
        opacity: 0.6
      }
    }
  }, {
    props: {
      isDayDisabled: true
    },
    style: {
      color: (theme.vars || theme).palette.text.disabled
    }
  }, {
    props: {
      isDayFillerCell: true
    },
    style: {
      // visibility: 'hidden' does not work here as it hides the element from screen readers
      // and results in unexpected relationships between week day and day columns.
      opacity: 0,
      pointerEvents: 'none'
    }
  }, {
    props: {
      isDayOutsideMonth: true
    },
    style: {
      color: (theme.vars || theme).palette.text.secondary
    }
  }, {
    props: {
      isDayCurrent: true,
      isDaySelected: false
    },
    style: {
      outline: `1px solid ${(theme.vars || theme).palette.text.secondary}`,
      outlineOffset: -1
    }
  }]
}));
const noop = () => {};
const PickerDay2Raw = /*#__PURE__*/React.forwardRef(function PickerDay2(inProps, forwardedRef) {
  const props = useThemeProps({
    props: inProps,
    name: 'MuiPickerDay2'
  });
  const adapter = usePickerAdapter();
  const {
      autoFocus = false,
      className,
      classes: classesProp,
      isAnimating,
      onClick,
      onDaySelect,
      onFocus = noop,
      onBlur = noop,
      onKeyDown = noop,
      onMouseDown = noop,
      onMouseEnter = noop,
      children,
      day,
      selected,
      disabled,
      today,
      outsideCurrentMonth,
      disableMargin,
      disableHighlightToday,
      showDaysOutsideCurrentMonth
    } = props,
    other = _objectWithoutPropertiesLoose(props, _excluded);
  const pickersDayOwnerState = usePickerDayOwnerState({
    day,
    selected,
    disabled,
    today,
    outsideCurrentMonth,
    disableMargin,
    disableHighlightToday,
    showDaysOutsideCurrentMonth
  });
  const ownerState = _extends({}, pickersDayOwnerState, {
    // Properties specific to the MUI implementation (some might be removed in the next major)
    isDayFillerCell: outsideCurrentMonth && !showDaysOutsideCurrentMonth
  });
  const classes = useUtilityClasses(ownerState, classesProp);
  const ref = React.useRef(null);
  const handleRef = useForkRef(ref, forwardedRef);

  // Since this is rendered when a Popper is opened we can't use passive effects.
  // Focusing in passive effects in Popper causes scroll jump.
  useEnhancedEffect(() => {
    if (autoFocus && !disabled && !isAnimating && !outsideCurrentMonth) {
      // ref.current being null would be a bug in MUI
      ref.current.focus();
    }
  }, [autoFocus, disabled, isAnimating, outsideCurrentMonth]);

  // For a day outside the current month, move the focus from mouseDown to mouseUp
  // Goal: have the onClick ends before sliding to the new month
  const handleMouseDown = event => {
    onMouseDown(event);
    if (outsideCurrentMonth) {
      event.preventDefault();
    }
  };
  const handleClick = event => {
    event.defaultMuiPrevented = true;
    if (!disabled) {
      onDaySelect(day);
    }
    if (outsideCurrentMonth) {
      event.currentTarget.focus();
    }
    if (onClick) {
      onClick(event);
    }
  };
  return /*#__PURE__*/_jsx(PickerDay2Root, _extends({
    ref: handleRef,
    centerRipple: true
    // compat with PickersDay for tests
    ,

    disabled: disabled,
    tabIndex: selected ? 0 : -1,
    onKeyDown: event => onKeyDown(event, day),
    onFocus: event => onFocus(event, day),
    onBlur: event => onBlur(event, day),
    onMouseEnter: event => onMouseEnter(event, day),
    onClick: handleClick,
    onMouseDown: handleMouseDown
  }, other, {
    ownerState: ownerState,
    className: clsx(classes.root, className),
    children: children ?? (ownerState.isDayFillerCell ? null : adapter.format(day, 'dayOfMonth'))
  }));
});
if (process.env.NODE_ENV !== "production") PickerDay2Raw.displayName = "PickerDay2Raw";
process.env.NODE_ENV !== "production" ? PickerDay2Raw.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * A ref for imperative actions.
   * It currently only supports `focusVisible()` action.
   */
  action: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({
    current: PropTypes.shape({
      focusVisible: PropTypes.func.isRequired
    })
  })]),
  /**
   * If `true`, the ripples are centered.
   * They won't start at the cursor interaction position.
   * @default false
   */
  centerRipple: PropTypes.bool,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: PropTypes.object,
  className: PropTypes.string,
  component: PropTypes.elementType,
  /**
   * The date to show.
   */
  day: PropTypes.object.isRequired,
  /**
   * If `true`, renders as disabled.
   * @default false
   */
  disabled: PropTypes.bool,
  /**
   * If `true`, today's date is rendering without highlighting with circle.
   * @default false
   */
  disableHighlightToday: PropTypes.bool,
  /**
   * If `true`, days are rendering without margin. Useful for displaying linked range of days.
   * @default false
   */
  disableMargin: PropTypes.bool,
  /**
   * If `true`, the ripple effect is disabled.
   *
   * ⚠️ Without a ripple there is no styling for :focus-visible by default. Be sure
   * to highlight the element by applying separate styles with the `.Mui-focusVisible` class.
   * @default false
   */
  disableRipple: PropTypes.bool,
  /**
   * If `true`, the touch ripple effect is disabled.
   * @default false
   */
  disableTouchRipple: PropTypes.bool,
  /**
   * If `true`, the base button will have a keyboard focus ripple.
   * @default false
   */
  focusRipple: PropTypes.bool,
  /**
   * This prop can help identify which element has keyboard focus.
   * The class name will be applied when the element gains the focus through keyboard interaction.
   * It's a polyfill for the [CSS :focus-visible selector](https://drafts.csswg.org/selectors-4/#the-focus-visible-pseudo).
   * The rationale for using this feature [is explained here](https://github.com/WICG/focus-visible/blob/HEAD/explainer.md).
   * A [polyfill can be used](https://github.com/WICG/focus-visible) to apply a `focus-visible` class to other components
   * if needed.
   */
  focusVisibleClassName: PropTypes.string,
  isAnimating: PropTypes.bool,
  /**
   * If `true`, day is the first visible cell of the month.
   * Either the first day of the month or the first day of the week depending on `showDaysOutsideCurrentMonth`.
   */
  isFirstVisibleCell: PropTypes.bool.isRequired,
  /**
   * If `true`, day is the last visible cell of the month.
   * Either the last day of the month or the last day of the week depending on `showDaysOutsideCurrentMonth`.
   */
  isLastVisibleCell: PropTypes.bool.isRequired,
  /**
   * Indicates if the day should be visually selected.
   */
  isVisuallySelected: PropTypes.bool,
  onBlur: PropTypes.func,
  onDaySelect: PropTypes.func.isRequired,
  onFocus: PropTypes.func,
  /**
   * Callback fired when the component is focused with a keyboard.
   * We trigger a `onFocus` callback too.
   */
  onFocusVisible: PropTypes.func,
  onKeyDown: PropTypes.func,
  onMouseEnter: PropTypes.func,
  /**
   * If `true`, day is outside of month and will be hidden.
   */
  outsideCurrentMonth: PropTypes.bool.isRequired,
  /**
   * If `true`, renders as selected.
   * @default false
   */
  selected: PropTypes.bool,
  /**
   * If `true`, days outside the current month are rendered:
   *
   * - if `fixedWeekNumber` is defined, renders days to have the weeks requested.
   *
   * - if `fixedWeekNumber` is not defined, renders day to fill the first and last week of the current month.
   *
   * - ignored if `calendars` equals more than `1` on range pickers.
   * @default false
   */
  showDaysOutsideCurrentMonth: PropTypes.bool,
  style: PropTypes.object,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),
  /**
   * @default 0
   */
  tabIndex: PropTypes.number,
  /**
   * If `true`, renders as today date.
   * @default false
   */
  today: PropTypes.bool,
  /**
   * Props applied to the `TouchRipple` element.
   */
  TouchRippleProps: PropTypes.object,
  /**
   * A ref that points to the `TouchRipple` element.
   */
  touchRippleRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({
    current: PropTypes.shape({
      pulsate: PropTypes.func.isRequired,
      start: PropTypes.func.isRequired,
      stop: PropTypes.func.isRequired
    })
  })])
} : void 0;
export const PickerDay2 = /*#__PURE__*/React.memo(PickerDay2Raw);
if (process.env.NODE_ENV !== "production") PickerDay2.displayName = "PickerDay2";