"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useMultiInputRangeFieldSelectedSections = void 0;
var React = _interopRequireWildcard(require("react"));
var _useForkRef = _interopRequireDefault(require("@mui/utils/useForkRef"));
var _useEventCallback = _interopRequireDefault(require("@mui/utils/useEventCallback"));
/**
 * @ignore - internal hook.
 */
const useMultiInputRangeFieldSelectedSections = parameters => {
  const unstableEndFieldRef = React.useRef(null);
  const handleUnstableEndFieldRef = (0, _useForkRef.default)(parameters.unstableEndFieldRef, unstableEndFieldRef);
  const [startSelectedSection, setStartSelectedSection] = React.useState(parameters.selectedSections ?? null);
  const [endSelectedSection, setEndSelectedSection] = React.useState(null);
  const getActiveField = () => {
    if (unstableEndFieldRef.current && unstableEndFieldRef.current.isFieldFocused()) {
      return 'end';
    }
    return 'start';
  };
  const handleStartSelectedSectionChange = (0, _useEventCallback.default)(newSelectedSections => {
    setStartSelectedSection(newSelectedSections);
    if (getActiveField() === 'start') {
      parameters.onSelectedSectionsChange?.(newSelectedSections);
    }
  });
  const handleEndSelectedSectionChange = (0, _useEventCallback.default)(newSelectedSections => {
    setEndSelectedSection(newSelectedSections);
    if (getActiveField() === 'end') {
      parameters.onSelectedSectionsChange?.(newSelectedSections);
    }
  });
  const activeField = getActiveField();
  return {
    start: {
      unstableFieldRef: parameters.unstableStartFieldRef,
      selectedSections: activeField === 'start' && parameters.selectedSections !== undefined ? parameters.selectedSections : startSelectedSection,
      onSelectedSectionsChange: handleStartSelectedSectionChange
    },
    end: {
      unstableFieldRef: handleUnstableEndFieldRef,
      selectedSections: activeField === 'end' && parameters.selectedSections !== undefined ? parameters.selectedSections : endSelectedSection,
      onSelectedSectionsChange: handleEndSelectedSectionChange
    }
  };
};
exports.useMultiInputRangeFieldSelectedSections = useMultiInputRangeFieldSelectedSections;