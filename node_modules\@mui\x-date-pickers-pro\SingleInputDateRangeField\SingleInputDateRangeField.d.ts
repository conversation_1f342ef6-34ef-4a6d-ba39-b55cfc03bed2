import * as React from 'react';
import { SingleInputDateRangeFieldProps } from "./SingleInputDateRangeField.types.js";
import { FieldType } from "../models/index.js";
type DateRangeFieldComponent = (<TEnableAccessibleFieldDOMStructure extends boolean = true>(props: SingleInputDateRangeFieldProps<TEnableAccessibleFieldDOMStructure> & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
  fieldType?: FieldType;
};
/**
 * Demos:
 *
 * - [DateRangeField](http://mui.com/x/react-date-pickers/date-range-field/)
 * - [Fields](https://mui.com/x/react-date-pickers/fields/)
 *
 * API:
 *
 * - [SingleInputDateRangeField API](https://mui.com/x/api/single-input-date-range-field/)
 */
declare const SingleInputDateRangeField: DateRangeFieldComponent;
export { SingleInputDateRangeField };