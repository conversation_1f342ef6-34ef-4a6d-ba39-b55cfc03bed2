import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Box,
  Grid,
  CircularProgress,
  Alert,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { ApartmentWithDetails, CheckoutApartmentRequest } from '../../../types/Apartment';
import { ApartmentService } from '../../services/apartmentService';

interface CheckoutFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: CheckoutApartmentRequest) => Promise<void>;
  apartment: ApartmentWithDetails;
  loading?: boolean;
}

interface FormData {
  endDate: Dayjs | null;
  checkoutReason: string;
}

const CheckoutForm: React.FC<CheckoutFormProps> = ({
  open,
  onClose,
  onSubmit,
  apartment,
  loading = false,
}) => {
  const [formData, setFormData] = useState<FormData>({
    endDate: dayjs(),
    checkoutReason: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data when dialog opens
  useEffect(() => {
    if (open) {
      setFormData({
        endDate: dayjs(),
        checkoutReason: '',
      });
      setErrors({});
    }
  }, [open]);

  const handleInputChange = (field: keyof FormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const value = event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleDateChange = (date: Dayjs | null) => {
    setFormData(prev => ({ ...prev, endDate: date }));
    
    // Clear error when date is selected
    if (errors.endDate) {
      setErrors(prev => ({ ...prev, endDate: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate end date
    if (!formData.endDate) {
      newErrors.endDate = 'Checkout date is required';
    } else if (formData.endDate.isAfter(dayjs(), 'day')) {
      newErrors.endDate = 'Checkout date cannot be in the future';
    }

    // Checkout reason is optional but validate length if provided
    if (formData.checkoutReason.trim().length > 500) {
      newErrors.checkoutReason = 'Checkout reason must be less than 500 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const submitData: CheckoutApartmentRequest = {
        apartmentId: apartment.id,
        endDate: formData.endDate!.toDate(),
        checkoutReason: formData.checkoutReason.trim() || undefined,
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error('Error submitting checkout form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      endDate: dayjs(),
      checkoutReason: '',
    });
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  const getQuickReasons = () => {
    return [
      'Lease expired',
      'Tenant moved out',
      'Contract terminated',
      'Property maintenance',
      'Tenant request',
      'Non-payment of rent',
      'Violation of lease terms',
    ];
  };

  const setQuickReason = (reason: string) => {
    setFormData(prev => ({ ...prev, checkoutReason: reason }));
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
          Checkout Apartment - {apartment.name}
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ pt: 2 }}>
          {/* Apartment and Customer Info */}
          <Alert severity="warning" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>Apartment:</strong> {apartment.name} ({apartment.size}, {apartment.numberOfRooms} rooms)
              <br />
              <strong>Current Tenant:</strong> {apartment.currentCustomer?.name || 'Unknown'}
              <br />
              <strong>Monthly Rent:</strong> {ApartmentService.formatCurrency(apartment.currentRate)}
            </Typography>
          </Alert>

          <Grid container spacing={3}>
            {/* Checkout Date */}
            <Grid item xs={12}>
              <DatePicker
                label="Checkout Date"
                value={formData.endDate}
                onChange={handleDateChange}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.endDate,
                    helperText: errors.endDate || 'Date when the tenant vacated the apartment',
                  },
                }}
                maxDate={dayjs()}
              />
            </Grid>

            {/* Quick Reason Buttons */}
            <Grid item xs={12}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Quick Reasons:
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                {getQuickReasons().map((reason) => (
                  <Button
                    key={reason}
                    size="small"
                    variant="outlined"
                    onClick={() => setQuickReason(reason)}
                    sx={{ textTransform: 'none' }}
                  >
                    {reason}
                  </Button>
                ))}
              </Box>
            </Grid>

            {/* Checkout Reason */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Checkout Reason (Optional)"
                value={formData.checkoutReason}
                onChange={handleInputChange('checkoutReason')}
                error={!!errors.checkoutReason}
                helperText={errors.checkoutReason || 'Reason for checkout (optional)'}
                multiline
                rows={3}
                placeholder="e.g., Lease expired, Tenant moved out, etc."
              />
            </Grid>

            {/* Warning Message */}
            <Grid item xs={12}>
              <Alert severity="error">
                <Typography variant="body2">
                  <strong>Warning:</strong> This action will mark the apartment as available and remove the current tenant assignment. 
                  This action cannot be undone. Please ensure all necessary procedures have been completed before proceeding.
                </Typography>
              </Alert>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button
          variant="contained"
          color="warning"
          onClick={handleSubmit}
          disabled={isSubmitting || loading}
          startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
        >
          {isSubmitting ? 'Processing...' : 'Checkout Apartment'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CheckoutForm;
