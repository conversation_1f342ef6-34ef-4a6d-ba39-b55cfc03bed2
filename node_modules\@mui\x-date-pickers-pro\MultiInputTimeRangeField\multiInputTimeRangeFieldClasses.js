"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.multiInputTimeRangeFieldClasses = exports.getMultiInputTimeRangeFieldUtilityClass = void 0;
var _generateUtilityClass = _interopRequireDefault(require("@mui/utils/generateUtilityClass"));
var _generateUtilityClasses = _interopRequireDefault(require("@mui/utils/generateUtilityClasses"));
const multiInputTimeRangeFieldClasses = exports.multiInputTimeRangeFieldClasses = (0, _generateUtilityClasses.default)('MuiMultiInputTimeRangeField', ['root', 'separator']);
const getMultiInputTimeRangeFieldUtilityClass = slot => (0, _generateUtilityClass.default)('MuiMultiInputTimeRangeField', slot);
exports.getMultiInputTimeRangeFieldUtilityClass = getMultiInputTimeRangeFieldUtilityClass;