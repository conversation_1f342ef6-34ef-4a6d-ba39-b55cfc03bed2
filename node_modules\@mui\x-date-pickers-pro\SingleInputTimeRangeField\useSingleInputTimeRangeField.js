"use strict";
'use client';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useSingleInputTimeRangeField = void 0;
var _internals = require("@mui/x-date-pickers/internals");
var _managers = require("../managers");
const useSingleInputTimeRangeField = props => {
  const manager = (0, _managers.useTimeRangeManager)(props);
  return (0, _internals.useField)({
    manager,
    props
  });
};
exports.useSingleInputTimeRangeField = useSingleInputTimeRangeField;