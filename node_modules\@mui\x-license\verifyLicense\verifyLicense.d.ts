import { LicenseStatus } from "../utils/licenseStatus.js";
import { MuiCommercialPackageName } from "../utils/commercialPackages.js";
export declare function generateReleaseInfo(releaseDate?: Date): string;
export declare function verifyLicense({
  releaseInfo,
  licenseKey,
  packageName
}: {
  releaseInfo: string;
  licenseKey?: string;
  packageName: MuiCommercialPackageName;
}): {
  status: LicenseStatus;
  meta?: any;
};