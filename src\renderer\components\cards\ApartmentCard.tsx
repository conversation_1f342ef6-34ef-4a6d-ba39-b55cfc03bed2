import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Box,
  Button,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Chip,
  Divider,
  Tooltip,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Home as HomeIcon,
  Person as PersonIcon,
  AttachMoney as MoneyIcon,
  MeetingRoom as RoomIcon,
  SquareFoot as SizeIcon,
  CheckCircle as OccupiedIcon,
  RadioButtonUnchecked as AvailableIcon,
  ExitToApp as CheckoutIcon,
  PersonAdd as BookIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { ApartmentWithDetails } from '../../../types/Apartment';
import { ApartmentService } from '../../services/apartmentService';

interface ApartmentCardProps {
  apartment: ApartmentWithDetails;
  onEdit: (apartment: ApartmentWithDetails) => void;
  onDelete: (apartment: ApartmentWithDetails) => void;
  onView: (apartment: ApartmentWithDetails) => void;
  onBook?: (apartment: ApartmentWithDetails) => void;
  onCheckout?: (apartment: ApartmentWithDetails) => void;
}

const ApartmentCard: React.FC<ApartmentCardProps> = ({
  apartment,
  onEdit,
  onDelete,
  onView,
  onBook,
  onCheckout,
}) => {
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    handleMenuClose();
    onEdit(apartment);
  };

  const handleDelete = () => {
    handleMenuClose();
    onDelete(apartment);
  };

  const handleView = () => {
    onView(apartment);
    navigate(`/apartments/${apartment.id}`);
  };

  const handleBook = () => {
    handleMenuClose();
    if (onBook) {
      onBook(apartment);
    }
  };

  const handleCheckout = () => {
    handleMenuClose();
    if (onCheckout) {
      onCheckout(apartment);
    }
  };

  const handleCardClick = () => {
    handleView();
  };

  const getStatusColor = (): 'success' | 'default' => {
    return apartment.isOccupied ? 'success' : 'default';
  };

  const getStatusIcon = () => {
    return apartment.isOccupied ? <OccupiedIcon /> : <AvailableIcon />;
  };

  const formatCurrency = (amount: number): string => {
    return ApartmentService.formatCurrency(amount);
  };

  return (
    <>
      <Card
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          cursor: 'pointer',
          transition: 'all 0.2s ease-in-out',
          border: apartment.isOccupied ? '2px solid #4caf50' : '2px solid #e0e0e0',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
          },
        }}
        onClick={handleCardClick}
      >
        <CardContent sx={{ flexGrow: 1, pb: 1 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
            <Avatar 
              sx={{ 
                bgcolor: apartment.isOccupied ? 'success.main' : 'grey.400', 
                mr: 2 
              }}
            >
              <HomeIcon />
            </Avatar>
            <Box sx={{ flexGrow: 1, minWidth: 0 }}>
              <Typography variant="h6" component="div" noWrap sx={{ fontWeight: 600 }}>
                {apartment.name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {apartment.floor?.name || 'Unknown Floor'}
              </Typography>
            </Box>
            <IconButton
              size="small"
              onClick={handleMenuOpen}
              sx={{ ml: 1 }}
            >
              <MoreVertIcon />
            </IconButton>
          </Box>

          {/* Status Chip */}
          <Box sx={{ mb: 2 }}>
            <Chip
              icon={getStatusIcon()}
              label={apartment.isOccupied ? 'Occupied' : 'Available'}
              color={getStatusColor()}
              size="small"
              sx={{ fontWeight: 600 }}
            />
          </Box>

          {/* Apartment Details */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 1 }}>
              <MoneyIcon fontSize="small" color="action" />
              <Typography variant="body2" sx={{ fontWeight: 600 }}>
                {formatCurrency(apartment.currentRate)}/month
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 1 }}>
              <SizeIcon fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary">
                {apartment.size}
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <RoomIcon fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary">
                {apartment.numberOfRooms} {apartment.numberOfRooms === 1 ? 'Room' : 'Rooms'}
              </Typography>
            </Box>
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Current Customer Info */}
          {apartment.isOccupied && apartment.currentCustomer ? (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <PersonIcon fontSize="small" color="action" />
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  {apartment.currentCustomer.name}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {apartment.currentCustomer.mobile}
                </Typography>
              </Box>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <AvailableIcon fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary">
                Ready for new tenant
              </Typography>
            </Box>
          )}
        </CardContent>

        <CardActions sx={{ pt: 0, px: 2, pb: 2 }}>
          <Button
            size="small"
            startIcon={<ViewIcon />}
            onClick={(e) => {
              e.stopPropagation();
              handleView();
            }}
          >
            View Details
          </Button>
          
          {apartment.isOccupied ? (
            <Tooltip title="Checkout Customer">
              <Button
                size="small"
                startIcon={<CheckoutIcon />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleCheckout();
                }}
                color="warning"
              >
                Checkout
              </Button>
            </Tooltip>
          ) : (
            <Tooltip title="Book Apartment">
              <Button
                size="small"
                startIcon={<BookIcon />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleBook();
                }}
                color="success"
              >
                Book
              </Button>
            </Tooltip>
          )}
        </CardActions>
      </Card>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        onClick={(e) => e.stopPropagation()}
      >
        <MenuItem onClick={handleView}>
          <ViewIcon sx={{ mr: 1 }} fontSize="small" />
          View Details
        </MenuItem>
        
        <MenuItem onClick={handleEdit}>
          <EditIcon sx={{ mr: 1 }} fontSize="small" />
          Edit Apartment
        </MenuItem>
        
        {apartment.isOccupied ? (
          <MenuItem onClick={handleCheckout}>
            <CheckoutIcon sx={{ mr: 1 }} fontSize="small" />
            Checkout Customer
          </MenuItem>
        ) : (
          <MenuItem onClick={handleBook}>
            <BookIcon sx={{ mr: 1 }} fontSize="small" />
            Book Apartment
          </MenuItem>
        )}
        
        <Divider />
        
        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} fontSize="small" />
          Delete Apartment
        </MenuItem>
      </Menu>
    </>
  );
};

export default ApartmentCard;
