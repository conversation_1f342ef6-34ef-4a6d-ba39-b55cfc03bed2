"use strict";
'use client';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useSingleInputDateTimeRangeField = void 0;
var _internals = require("@mui/x-date-pickers/internals");
var _managers = require("../managers");
const useSingleInputDateTimeRangeField = props => {
  const manager = (0, _managers.useDateTimeRangeManager)(props);
  return (0, _internals.useField)({
    manager,
    props
  });
};
exports.useSingleInputDateTimeRangeField = useSingleInputDateTimeRangeField;