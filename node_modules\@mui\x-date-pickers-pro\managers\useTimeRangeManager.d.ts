import type { MakeOptional } from '@mui/x-internals/types';
import { PickerManager } from '@mui/x-date-pickers/models';
import { AmPmProps, PickerRangeValue, UseFieldInternalProps } from '@mui/x-date-pickers/internals';
import { TimeRangeValidationError, RangeFieldSeparatorProps } from "../models/index.js";
import { ExportedValidateTimeRangeProps, ValidateTimeRangeProps } from "../validation/validateTimeRange.js";
export declare function useTimeRangeManager<TEnableAccessibleFieldDOMStructure extends boolean = true>(parameters?: UseTimeRangeManagerParameters<TEnableAccessibleFieldDOMStructure>): UseTimeRangeManagerReturnValue<TEnableAccessibleFieldDOMStructure>;
export interface UseTimeRangeManagerParameters<TEnableAccessibleFieldDOMStructure extends boolean> extends RangeFieldSeparatorProps, AmPmProps {
  enableAccessibleFieldDOMStructure?: TEnableAccessibleFieldDOMStructure;
}
export type UseTimeRangeManagerReturnValue<TEnableAccessibleFieldDOMStructure extends boolean> = PickerManager<PickerRangeValue, TEnableAccessibleFieldDOMStructure, TimeRangeValidationError, ValidateTimeRangeProps, TimeRangeManagerFieldInternalProps<TEnableAccessibleFieldDOMStructure>>;
export interface TimeRangeManagerFieldInternalProps<TEnableAccessibleFieldDOMStructure extends boolean> extends MakeOptional<UseFieldInternalProps<PickerRangeValue, TEnableAccessibleFieldDOMStructure, TimeRangeValidationError>, 'format'>, ExportedValidateTimeRangeProps, AmPmProps, RangeFieldSeparatorProps {}