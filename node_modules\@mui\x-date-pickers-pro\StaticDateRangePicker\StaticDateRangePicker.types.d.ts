import { MakeOptional } from '@mui/x-internals/types';
import { StaticRangeOnlyPickerProps, UseStaticRangePickerSlots, UseStaticRangePickerSlotProps } from "../internals/hooks/useStaticRangePicker/index.js";
import { BaseDateRangePickerProps, BaseDateRangePickerSlots, BaseDateRangePickerSlotProps } from "../DateRangePicker/shared.js";
export interface StaticDateRangePickerSlots extends BaseDateRangePickerSlots, UseStaticRangePickerSlots {}
export interface StaticDateRangePickerSlotProps extends BaseDateRangePickerSlotProps, Omit<UseStaticRangePickerSlotProps, 'toolbar'> {}
export interface StaticDateRangePickerProps extends BaseDateRangePickerProps, MakeOptional<StaticRangeOnlyPickerProps, 'displayStaticWrapperAs'> {
  /**
   * The number of calendars to render.
   * @default 1 if `displayStaticWrapperAs === 'mobile'`, 2 otherwise.
   */
  calendars?: 1 | 2 | 3;
  /**
   * Overridable component slots.
   * @default {}
   */
  slots?: StaticDateRangePickerSlots;
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps?: StaticDateRangePickerSlotProps;
}