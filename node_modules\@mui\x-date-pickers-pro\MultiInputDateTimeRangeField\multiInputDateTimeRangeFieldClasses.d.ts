import { MultiInputRangeFieldClasses } from "../internals/utils/createMultiInputRangeField/index.js";
export interface MultiInputDateTimeRangeFieldClasses extends MultiInputRangeFieldClasses {}
export type MultiInputDateTimeRangeFieldClassKey = keyof MultiInputRangeFieldClasses;
export declare const multiInputDateTimeRangeFieldClasses: MultiInputRangeFieldClasses;
export declare const getMultiInputDateTimeRangeFieldUtilityClass: (slot: string) => string;