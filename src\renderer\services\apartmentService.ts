import { 
  Apartment, 
  CreateApartmentRequest, 
  UpdateApartmentRequest, 
  ApartmentWithDetails,
  ApartmentBookingHistory,
  BookApartmentRequest,
  CheckoutApartmentRequest
} from '../../types/Apartment';
import { ApiResponse } from '../../types/common';

export class ApartmentService {
  // Create a new apartment
  static async createApartment(apartmentData: CreateApartmentRequest): Promise<ApiResponse<Apartment>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.apartments.create(apartmentData);
      return result;
    } catch (error) {
      console.error('Error creating apartment:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create apartment',
      };
    }
  }

  // Get all apartments for a floor
  static async getApartmentsByFloor(floorId: number): Promise<ApiResponse<ApartmentWithDetails[]>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.apartments.getByFloor(floorId);
      return result;
    } catch (error) {
      console.error('Error fetching apartments:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch apartments',
      };
    }
  }

  // Get apartment with full details
  static async getApartmentWithDetails(id: number): Promise<ApiResponse<ApartmentWithDetails>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.apartments.getWithDetails(id);
      return result;
    } catch (error) {
      console.error('Error fetching apartment details:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch apartment details',
      };
    }
  }

  // Update apartment
  static async updateApartment(apartmentData: UpdateApartmentRequest): Promise<ApiResponse<Apartment>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.apartments.update(apartmentData);
      return result;
    } catch (error) {
      console.error('Error updating apartment:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update apartment',
      };
    }
  }

  // Delete apartment
  static async deleteApartment(id: number): Promise<ApiResponse<void>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.apartments.delete(id);
      return result;
    } catch (error) {
      console.error('Error deleting apartment:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete apartment',
      };
    }
  }

  // Book apartment
  static async bookApartment(bookingData: BookApartmentRequest): Promise<ApiResponse<ApartmentBookingHistory>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.apartments.book(bookingData);
      return result;
    } catch (error) {
      console.error('Error booking apartment:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to book apartment',
      };
    }
  }

  // Checkout apartment
  static async checkoutApartment(checkoutData: CheckoutApartmentRequest): Promise<ApiResponse<void>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.apartments.checkout(checkoutData);
      return result;
    } catch (error) {
      console.error('Error checking out apartment:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to checkout apartment',
      };
    }
  }

  // Get apartment booking history
  static async getApartmentHistory(apartmentId: number): Promise<ApiResponse<ApartmentBookingHistory[]>> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.apartments.getHistory(apartmentId);
      return result;
    } catch (error) {
      console.error('Error fetching apartment history:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch apartment history',
      };
    }
  }

  // Validate apartment data
  static validateApartmentData(data: CreateApartmentRequest | UpdateApartmentRequest): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    // Validate name
    if (!data.name || data.name.trim().length === 0) {
      errors.name = 'Apartment name is required';
    } else if (data.name.trim().length > 100) {
      errors.name = 'Apartment name must be less than 100 characters';
    }

    // Validate current rate
    if (data.currentRate === undefined || data.currentRate === null) {
      errors.currentRate = 'Current rate is required';
    } else if (data.currentRate <= 0) {
      errors.currentRate = 'Current rate must be greater than 0';
    } else if (data.currentRate > 1000000) {
      errors.currentRate = 'Current rate cannot exceed 1,000,000';
    }

    // Validate size
    if (!data.size || data.size.trim().length === 0) {
      errors.size = 'Apartment size is required';
    } else if (data.size.trim().length > 50) {
      errors.size = 'Apartment size must be less than 50 characters';
    }

    // Validate number of rooms
    if (data.numberOfRooms === undefined || data.numberOfRooms === null) {
      errors.numberOfRooms = 'Number of rooms is required';
    } else if (data.numberOfRooms <= 0) {
      errors.numberOfRooms = 'Number of rooms must be greater than 0';
    } else if (data.numberOfRooms > 20) {
      errors.numberOfRooms = 'Number of rooms cannot exceed 20';
    }

    // Validate floor ID for create requests
    if ('floorId' in data) {
      if (!data.floorId || data.floorId <= 0) {
        errors.floorId = 'Valid floor ID is required';
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }

  // Generate apartment name based on floor and position
  static generateApartmentName(floorNumber: number, position: number): string {
    const floorPrefix = floorNumber === 0 ? 'G' : floorNumber.toString();
    const positionSuffix = position.toString().padStart(2, '0');
    return `${floorPrefix}${positionSuffix}`;
  }

  // Calculate apartment statistics
  static calculateApartmentStats(apartments: ApartmentWithDetails[]): {
    totalApartments: number;
    occupiedApartments: number;
    availableApartments: number;
    occupancyRate: number;
    totalRentCollection: number;
    averageRent: number;
  } {
    const totalApartments = apartments.length;
    const occupiedApartments = apartments.filter(apt => apt.isOccupied).length;
    const availableApartments = totalApartments - occupiedApartments;
    const occupancyRate = totalApartments > 0 ? (occupiedApartments / totalApartments) * 100 : 0;
    const totalRentCollection = apartments
      .filter(apt => apt.isOccupied)
      .reduce((sum, apt) => sum + apt.currentRate, 0);
    const averageRent = totalApartments > 0 
      ? apartments.reduce((sum, apt) => sum + apt.currentRate, 0) / totalApartments 
      : 0;

    return {
      totalApartments,
      occupiedApartments,
      availableApartments,
      occupancyRate,
      totalRentCollection,
      averageRent,
    };
  }

  // Sort apartments by name
  static sortApartmentsByName(apartments: ApartmentWithDetails[]): ApartmentWithDetails[] {
    return [...apartments].sort((a, b) => a.name.localeCompare(b.name));
  }

  // Filter apartments by search term
  static filterApartments(apartments: ApartmentWithDetails[], searchTerm: string): ApartmentWithDetails[] {
    if (!searchTerm.trim()) {
      return apartments;
    }

    const term = searchTerm.toLowerCase().trim();
    return apartments.filter(apartment =>
      apartment.name.toLowerCase().includes(term) ||
      apartment.size.toLowerCase().includes(term) ||
      apartment.numberOfRooms.toString().includes(term) ||
      apartment.currentCustomer?.name.toLowerCase().includes(term) ||
      apartment.currentRate.toString().includes(term)
    );
  }

  // Filter apartments by status
  static filterApartmentsByStatus(apartments: ApartmentWithDetails[], status: 'all' | 'occupied' | 'available'): ApartmentWithDetails[] {
    if (status === 'all') {
      return apartments;
    }
    return apartments.filter(apartment => 
      status === 'occupied' ? apartment.isOccupied : !apartment.isOccupied
    );
  }

  // Get apartment status display text
  static getApartmentStatusText(apartment: ApartmentWithDetails): string {
    if (apartment.isOccupied && apartment.currentCustomer) {
      return `Occupied by ${apartment.currentCustomer.name}`;
    } else if (apartment.isOccupied) {
      return 'Occupied';
    } else {
      return 'Available';
    }
  }

  // Format currency for display
  static formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  }

  // Validate booking data
  static validateBookingData(data: BookApartmentRequest): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    if (!data.apartmentId || data.apartmentId <= 0) {
      errors.apartmentId = 'Valid apartment ID is required';
    }

    if (!data.customerId || data.customerId <= 0) {
      errors.customerId = 'Valid customer ID is required';
    }

    if (!data.startDate) {
      errors.startDate = 'Start date is required';
    } else if (data.startDate < new Date()) {
      errors.startDate = 'Start date cannot be in the past';
    }

    if (data.rentAmount === undefined || data.rentAmount === null || data.rentAmount <= 0) {
      errors.rentAmount = 'Valid rent amount is required';
    }

    if (data.advanceAmount === undefined || data.advanceAmount === null || data.advanceAmount < 0) {
      errors.advanceAmount = 'Valid advance amount is required';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }

  // Export apartments data for reporting
  static exportApartmentsData(apartments: ApartmentWithDetails[]): any[] {
    return apartments.map(apartment => ({
      id: apartment.id,
      name: apartment.name,
      currentRate: apartment.currentRate,
      size: apartment.size,
      numberOfRooms: apartment.numberOfRooms,
      floorName: apartment.floor?.name || 'Unknown',
      buildingName: apartment.building?.name || 'Unknown',
      isOccupied: apartment.isOccupied,
      currentCustomer: apartment.currentCustomer?.name || 'N/A',
      status: this.getApartmentStatusText(apartment),
      createdAt: apartment.createdAt.toISOString(),
      updatedAt: apartment.updatedAt.toISOString(),
    }));
  }
}
