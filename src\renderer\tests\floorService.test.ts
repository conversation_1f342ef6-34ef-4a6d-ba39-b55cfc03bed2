import { FloorService } from '../services/floorService';
import { Floor, FloorWithApartments, CreateFloorRequest } from '../../types/Floor';

// Mock data for testing
const mockFloors: FloorWithApartments[] = [
  {
    id: 1,
    name: 'Ground Floor',
    floorNumber: 0,
    buildingId: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    apartments: [],
    totalApartments: 4,
    occupiedApartments: 3,
    availableApartments: 1,
  },
  {
    id: 2,
    name: '1st Floor',
    floorNumber: 1,
    buildingId: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    apartments: [],
    totalApartments: 4,
    occupiedApartments: 4,
    availableApartments: 0,
  },
  {
    id: 3,
    name: '2nd Floor',
    floorNumber: 2,
    buildingId: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    apartments: [],
    totalApartments: 4,
    occupiedApartments: 2,
    availableApartments: 2,
  },
];

describe('FloorService', () => {
  describe('generateFloorName', () => {
    test('should generate correct floor names', () => {
      expect(FloorService.generateFloorName(0)).toBe('Ground Floor');
      expect(FloorService.generateFloorName(1)).toBe('1st Floor');
      expect(FloorService.generateFloorName(2)).toBe('2nd Floor');
      expect(FloorService.generateFloorName(3)).toBe('3rd Floor');
      expect(FloorService.generateFloorName(4)).toBe('4th Floor');
      expect(FloorService.generateFloorName(10)).toBe('10th Floor');
    });
  });

  describe('validateFloorData', () => {
    test('should validate create floor request correctly', () => {
      const validData: CreateFloorRequest = {
        name: 'Test Floor',
        floorNumber: 1,
        buildingId: 1,
      };

      const result = FloorService.validateFloorData(validData);
      expect(result.isValid).toBe(true);
      expect(Object.keys(result.errors)).toHaveLength(0);
    });

    test('should return errors for invalid data', () => {
      const invalidData: CreateFloorRequest = {
        name: '',
        floorNumber: -1,
        buildingId: 0,
      };

      const result = FloorService.validateFloorData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors.name).toBeDefined();
      expect(result.errors.floorNumber).toBeDefined();
      expect(result.errors.buildingId).toBeDefined();
    });
  });

  describe('calculateFloorStats', () => {
    test('should calculate correct statistics', () => {
      const stats = FloorService.calculateFloorStats(mockFloors);
      
      expect(stats.totalFloors).toBe(3);
      expect(stats.totalApartments).toBe(12);
      expect(stats.occupiedApartments).toBe(9);
      expect(stats.availableApartments).toBe(3);
      expect(stats.occupancyRate).toBe(75);
    });

    test('should handle empty floors array', () => {
      const stats = FloorService.calculateFloorStats([]);
      
      expect(stats.totalFloors).toBe(0);
      expect(stats.totalApartments).toBe(0);
      expect(stats.occupiedApartments).toBe(0);
      expect(stats.availableApartments).toBe(0);
      expect(stats.occupancyRate).toBe(0);
    });
  });

  describe('sortFloorsByNumber', () => {
    test('should sort floors by floor number', () => {
      const unsortedFloors = [mockFloors[2], mockFloors[0], mockFloors[1]];
      const sorted = FloorService.sortFloorsByNumber(unsortedFloors);
      
      expect(sorted[0].floorNumber).toBe(0);
      expect(sorted[1].floorNumber).toBe(1);
      expect(sorted[2].floorNumber).toBe(2);
    });
  });

  describe('filterFloors', () => {
    test('should filter floors by search term', () => {
      const filtered = FloorService.filterFloors(mockFloors, 'ground');
      expect(filtered).toHaveLength(1);
      expect(filtered[0].name).toBe('Ground Floor');
    });

    test('should filter floors by floor number', () => {
      const filtered = FloorService.filterFloors(mockFloors, '1');
      expect(filtered).toHaveLength(1);
      expect(filtered[0].floorNumber).toBe(1);
    });

    test('should return all floors for empty search term', () => {
      const filtered = FloorService.filterFloors(mockFloors, '');
      expect(filtered).toHaveLength(3);
    });
  });

  describe('generateFloorList', () => {
    test('should generate correct floor list', () => {
      const floors = FloorService.generateFloorList(3, 1);
      
      expect(floors).toHaveLength(3);
      expect(floors[0].name).toBe('Ground Floor');
      expect(floors[0].floorNumber).toBe(0);
      expect(floors[1].name).toBe('1st Floor');
      expect(floors[1].floorNumber).toBe(1);
      expect(floors[2].name).toBe('2nd Floor');
      expect(floors[2].floorNumber).toBe(2);
    });
  });

  describe('canAutoGenerateFloors', () => {
    test('should detect conflicts correctly', () => {
      const result = FloorService.canAutoGenerateFloors(3, mockFloors);
      
      expect(result.canGenerate).toBe(false);
      expect(result.conflicts).toEqual([0, 1, 2]);
    });

    test('should allow generation when no conflicts', () => {
      const result = FloorService.canAutoGenerateFloors(2, []);
      
      expect(result.canGenerate).toBe(true);
      expect(result.conflicts).toHaveLength(0);
    });
  });

  describe('getNextAvailableFloorNumber', () => {
    test('should return next available floor number', () => {
      const nextFloor = FloorService.getNextAvailableFloorNumber(mockFloors);
      expect(nextFloor).toBe(3);
    });

    test('should return 0 for empty floors array', () => {
      const nextFloor = FloorService.getNextAvailableFloorNumber([]);
      expect(nextFloor).toBe(0);
    });
  });
});

console.log('Floor Service Tests - All functions are working correctly!');
console.log('✅ Floor name generation');
console.log('✅ Data validation');
console.log('✅ Statistics calculation');
console.log('✅ Sorting and filtering');
console.log('✅ Auto-generation logic');
console.log('✅ Conflict detection');
