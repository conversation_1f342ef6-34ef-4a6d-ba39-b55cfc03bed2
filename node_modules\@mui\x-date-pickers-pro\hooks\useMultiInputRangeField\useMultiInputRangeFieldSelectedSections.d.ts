import * as React from 'react';
import { PickerRangeValue, <PERSON>erValue, UseFieldInternalProps } from '@mui/x-date-pickers/internals';
import { FieldRef, FieldSelectedSections } from '@mui/x-date-pickers/models';
import { MultiInputFieldRefs } from "../../models/index.js";
interface UseMultiInputRangeFieldSelectedSectionsParameters extends Pick<UseFieldInternalProps<PickerRangeValue, any, any>, 'selectedSections' | 'onSelectedSectionsChange'>, MultiInputFieldRefs {}
export interface UseMultiInputFieldSelectedSectionsResponseItem {
  unstableFieldRef?: React.Ref<FieldRef<PickerValue>>;
  selectedSections: FieldSelectedSections;
  onSelectedSectionsChange: (newSelectedSections: FieldSelectedSections) => void;
}
interface UseMultiInputFieldSelectedSectionsResponse {
  start: UseMultiInputFieldSelectedSectionsResponseItem;
  end: UseMultiInputFieldSelectedSectionsResponseItem;
}
/**
 * @ignore - internal hook.
 */
export declare const useMultiInputRangeFieldSelectedSections: (parameters: UseMultiInputRangeFieldSelectedSectionsParameters) => UseMultiInputFieldSelectedSectionsResponse;
export {};