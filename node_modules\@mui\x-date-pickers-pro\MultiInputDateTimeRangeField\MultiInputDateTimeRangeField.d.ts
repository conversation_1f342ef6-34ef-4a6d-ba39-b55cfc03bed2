import * as React from 'react';
import { UseDateTimeRangeManagerReturnValue } from "../managers/index.js";
import { MultiInputRangeFieldProps } from "../internals/utils/createMultiInputRangeField/index.js";
import { MultiInputDateTimeRangeFieldClasses } from "./multiInputDateTimeRangeFieldClasses.js";
export interface MultiInputDateTimeRangeFieldProps<TEnableAccessibleFieldDOMStructure extends boolean> extends MultiInputRangeFieldProps<UseDateTimeRangeManagerReturnValue<TEnableAccessibleFieldDOMStructure>> {
  /**
   * Override or extend the styles applied to the component.
   */
  classes?: Partial<MultiInputDateTimeRangeFieldClasses>;
}
type MultiInputDateTimeRangeFieldComponent = (<TEnableAccessibleFieldDOMStructure extends boolean = true>(props: MultiInputDateTimeRangeFieldProps<TEnableAccessibleFieldDOMStructure> & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
};
/**
 * Demos:
 *
 * - [DateTimeRangeField](http://mui.com/x/react-date-pickers/date-time-range-field/)
 * - [Fields](https://mui.com/x/react-date-pickers/fields/)
 *
 * API:
 *
 * - [MultiInputDateTimeRangeField API](https://mui.com/x/api/multi-input-date-time-range-field/)
 */
declare const MultiInputDateTimeRangeField: MultiInputDateTimeRangeFieldComponent;
export { MultiInputDateTimeRangeField };