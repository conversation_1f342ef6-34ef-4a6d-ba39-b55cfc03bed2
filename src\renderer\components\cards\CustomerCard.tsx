import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Box,
  Button,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Chip,
  Divider,
  Tooltip,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Work as WorkIcon,
  Home as HomeIcon,
  AttachMoney as MoneyIcon,
  CalendarToday as CalendarIcon,
  CheckCircle as ActiveIcon,
  RadioButtonUnchecked as InactiveIcon,
  Assignment as DocumentIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { CustomerWithCurrentApartment } from '../../../types/Customer';
import { CustomerService } from '../../services/customerService';

interface CustomerCardProps {
  customer: CustomerWithCurrentApartment;
  onEdit: (customer: CustomerWithCurrentApartment) => void;
  onDelete: (customer: CustomerWithCurrentApartment) => void;
  onView: (customer: CustomerWithCurrentApartment) => void;
}

const CustomerCard: React.FC<CustomerCardProps> = ({
  customer,
  onEdit,
  onDelete,
  onView,
}) => {
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    handleMenuClose();
    onEdit(customer);
  };

  const handleDelete = () => {
    handleMenuClose();
    onDelete(customer);
  };

  const handleView = () => {
    onView(customer);
    navigate(`/customers/${customer.id}`);
  };

  const handleCardClick = () => {
    handleView();
  };

  const getStatusColor = (): 'success' | 'default' => {
    return customer.isActive ? 'success' : 'default';
  };

  const getStatusIcon = () => {
    return customer.isActive ? <ActiveIcon /> : <InactiveIcon />;
  };

  const formatCurrency = (amount: number): string => {
    return CustomerService.formatCurrency(amount);
  };

  const formatDate = (date: Date): string => {
    return CustomerService.formatDate(date);
  };

  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <>
      <Card
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          cursor: 'pointer',
          transition: 'all 0.2s ease-in-out',
          border: customer.isActive ? '2px solid #4caf50' : '2px solid #e0e0e0',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
          },
        }}
        onClick={handleCardClick}
      >
        <CardContent sx={{ flexGrow: 1, pb: 1 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
            <Avatar 
              sx={{ 
                bgcolor: customer.isActive ? 'success.main' : 'grey.400', 
                mr: 2,
                width: 48,
                height: 48,
              }}
            >
              {customer.photoPath ? (
                <img 
                  src={customer.photoPath} 
                  alt={customer.name}
                  style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                />
              ) : (
                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                  {getInitials(customer.name)}
                </Typography>
              )}
            </Avatar>
            <Box sx={{ flexGrow: 1, minWidth: 0 }}>
              <Typography variant="h6" component="div" noWrap sx={{ fontWeight: 600 }}>
                {customer.name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {customer.occupation}
              </Typography>
            </Box>
            <IconButton
              size="small"
              onClick={handleMenuOpen}
              sx={{ ml: 1 }}
            >
              <MoreVertIcon />
            </IconButton>
          </Box>

          {/* Status Chip */}
          <Box sx={{ mb: 2 }}>
            <Chip
              icon={getStatusIcon()}
              label={CustomerService.getCustomerStatusText(customer)}
              color={getStatusColor()}
              size="small"
              sx={{ fontWeight: 600 }}
            />
          </Box>

          {/* Contact Information */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 1 }}>
              <PhoneIcon fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary">
                {customer.mobile}
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 1 }}>
              <HomeIcon fontSize="small" color="action" />
              <Typography 
                variant="body2" 
                color="text.secondary"
                sx={{ 
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
              >
                {customer.address}
              </Typography>
            </Box>
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Financial Information */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 1 }}>
              <MoneyIcon fontSize="small" color="action" />
              <Typography variant="body2" sx={{ fontWeight: 600 }}>
                {formatCurrency(customer.rent)}/month
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 1 }}>
              <CalendarIcon fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary">
                Since {formatDate(customer.startDate)}
              </Typography>
            </Box>
          </Box>

          {/* Current Apartment Info */}
          {customer.isActive && customer.currentApartment ? (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <HomeIcon fontSize="small" color="success" />
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  Apartment {customer.currentApartment.name}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {customer.currentApartment.floor?.name} • {customer.currentApartment.building?.name}
                </Typography>
              </Box>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <InactiveIcon fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary">
                Not currently renting
              </Typography>
            </Box>
          )}

          {/* Document Indicators */}
          <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
            {customer.nidPath && (
              <Tooltip title="NID Document Available">
                <Chip
                  icon={<DocumentIcon />}
                  label="NID"
                  size="small"
                  variant="outlined"
                  color="primary"
                />
              </Tooltip>
            )}
            {customer.photoPath && (
              <Tooltip title="Photo Available">
                <Chip
                  icon={<PersonIcon />}
                  label="Photo"
                  size="small"
                  variant="outlined"
                  color="secondary"
                />
              </Tooltip>
            )}
          </Box>
        </CardContent>

        <CardActions sx={{ pt: 0, px: 2, pb: 2 }}>
          <Button
            size="small"
            startIcon={<ViewIcon />}
            onClick={(e) => {
              e.stopPropagation();
              handleView();
            }}
          >
            View Details
          </Button>
          <Button
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              handleEdit();
            }}
          >
            Edit
          </Button>
        </CardActions>
      </Card>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        onClick={(e) => e.stopPropagation()}
      >
        <MenuItem onClick={handleView}>
          <ViewIcon sx={{ mr: 1 }} fontSize="small" />
          View Details
        </MenuItem>
        
        <MenuItem onClick={handleEdit}>
          <EditIcon sx={{ mr: 1 }} fontSize="small" />
          Edit Customer
        </MenuItem>
        
        <Divider />
        
        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} fontSize="small" />
          Delete Customer
        </MenuItem>
      </Menu>
    </>
  );
};

export default CustomerCard;
