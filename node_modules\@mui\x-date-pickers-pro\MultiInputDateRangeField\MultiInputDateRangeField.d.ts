import * as React from 'react';
import { UseDateRangeManagerReturnValue } from "../managers/index.js";
import { MultiInputRangeFieldProps } from "../internals/utils/createMultiInputRangeField/index.js";
import { MultiInputDateRangeFieldClasses } from "./multiInputDateRangeFieldClasses.js";
export interface MultiInputDateRangeFieldProps<TEnableAccessibleFieldDOMStructure extends boolean> extends MultiInputRangeFieldProps<UseDateRangeManagerReturnValue<TEnableAccessibleFieldDOMStructure>> {
  /**
   * Override or extend the styles applied to the component.
   */
  classes?: Partial<MultiInputDateRangeFieldClasses>;
}
type MultiInputDateRangeFieldComponent = (<TEnableAccessibleFieldDOMStructure extends boolean = true>(props: MultiInputDateRangeFieldProps<TEnableAccessibleFieldDOMStructure> & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
};
/**
 * Demos:
 *
 * - [DateRangeField](http://mui.com/x/react-date-pickers/date-range-field/)
 * - [Fields](https://mui.com/x/react-date-pickers/fields/)
 *
 * API:
 *
 * - [MultiInputDateRangeField API](https://mui.com/x/api/multi-input-date-range-field/)
 */
declare const MultiInputDateRangeField: MultiInputDateRangeFieldComponent;
export { MultiInputDateRangeField };