import * as React from 'react';
import { MobileTimeRangePickerProps } from "./MobileTimeRangePicker.types.js";
type MobileTimeRangePickerComponent = (<TEnableAccessibleFieldDOMStructure extends boolean = true>(props: MobileTimeRangePickerProps<TEnableAccessibleFieldDOMStructure> & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
};
declare const MobileTimeRangePicker: MobileTimeRangePickerComponent;
export { MobileTimeRangePicker };