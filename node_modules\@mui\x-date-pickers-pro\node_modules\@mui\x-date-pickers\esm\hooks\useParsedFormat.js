'use client';

import * as React from 'react';
import { useRtl } from '@mui/system/RtlProvider';
import { usePickerAdapter } from "./usePickerAdapter.js";
import { buildSectionsFromFormat } from "../internals/hooks/useField/buildSectionsFromFormat.js";
import { getLocalizedDigits } from "../internals/hooks/useField/useField.utils.js";
import { usePickerTranslations } from "./usePickerTranslations.js";
import { useNullablePickerContext } from "../internals/hooks/useNullablePickerContext.js";
/**
 * Returns the parsed format to be rendered in the field when there is no value or in other parts of the Picker.
 * This format is localized (for example `AAAA` for the year with the French locale) and cannot be parsed by your date library.
 * @param {object} The parameters needed to build the placeholder.
 * @param {string} params.format Format to parse.
 * @returns
 */
export const useParsedFormat = (parameters = {}) => {
  const pickerContext = useNullablePickerContext();
  const adapter = usePickerAdapter();
  const isRtl = useRtl();
  const translations = usePickerTranslations();
  const localizedDigits = React.useMemo(() => getLocalizedDigits(adapter), [adapter]);
  const {
    format = pickerContext?.fieldFormat ?? adapter.formats.fullDate
  } = parameters;
  return React.useMemo(() => {
    const sections = buildSectionsFromFormat({
      adapter,
      format,
      formatDensity: 'dense',
      isRtl,
      shouldRespectLeadingZeros: true,
      localeText: translations,
      localizedDigits,
      date: null,
      // TODO v9: Make sure we still don't reverse in `buildSectionsFromFormat` when using `useParsedFormat`.
      enableAccessibleFieldDOMStructure: false
    });
    return sections.map(section => `${section.startSeparator}${section.placeholder}${section.endSeparator}`).join('');
  }, [adapter, isRtl, translations, localizedDigits, format]);
};