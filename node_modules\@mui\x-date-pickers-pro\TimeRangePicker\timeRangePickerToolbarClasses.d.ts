export interface TimeRangePickerToolbarClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the container element. */
  container: string;
  /** Styles applied to the separator element. */
  separator: string;
  /** Styles applied to the time container element. */
  timeContainer: string;
}
export type TimeRangePickerToolbarClassKey = keyof TimeRangePickerToolbarClasses;
export declare function getTimeRangePickerToolbarUtilityClass(slot: string): string;
export declare const timeRangePickerToolbarClasses: TimeRangePickerToolbarClasses;