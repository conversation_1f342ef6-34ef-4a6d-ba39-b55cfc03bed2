import * as React from 'react';
import { ExportedBaseTabsProps } from '@mui/x-date-pickers/internals';
import { TimeRangePickerTabsClasses } from "./timeRangePickerTabsClasses.js";
export interface ExportedTimeRangePickerTabsProps extends ExportedBaseTabsProps {
  /**
   * Toggles visibility of the tabs allowing view switching.
   * @default `window.innerHeight < 667` for `DesktopTimeRangePicker` and `MobileTimeRangePicker`, `displayStaticWrapperAs === 'desktop'` for `StaticTimeRangePicker`
   */
  hidden?: boolean;
  /**
   * Time tab icon.
   * @default Time
   */
  timeIcon?: React.ReactElement<any>;
  /**
   * Override or extend the styles applied to the component.
   */
  classes?: Partial<TimeRangePickerTabsClasses>;
}
export interface TimeRangePickerTabsProps extends ExportedTimeRangePickerTabsProps {}
/**
 * Demos:
 *
 * - [TimeRangePicker](https://mui.com/x/react-date-pickers/time-range-picker/)
 * - [Custom slots and subcomponents](https://mui.com/x/react-date-pickers/custom-components/)
 *
 * API:
 *
 * - [TimeRangePickerTabs API](https://mui.com/x/api/date-pickers/time-range-picker-tabs/)
 */
declare const TimeRangePickerTabs: {
  (inProps: TimeRangePickerTabsProps): React.JSX.Element | null;
  propTypes: any;
};
export { TimeRangePickerTabs };