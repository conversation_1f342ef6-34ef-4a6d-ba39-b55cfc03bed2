"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TimeRangePickerToolbar = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _propTypes = _interopRequireDefault(require("prop-types"));
var _clsx = _interopRequireDefault(require("clsx"));
var _styles = require("@mui/material/styles");
var _composeClasses = _interopRequireDefault(require("@mui/utils/composeClasses"));
var _internals = require("@mui/x-date-pickers/internals");
var _hooks = require("@mui/x-date-pickers/hooks");
var _timeRangePickerToolbarClasses = require("./timeRangePickerToolbarClasses");
var _hooks2 = require("../hooks");
var _jsxRuntime = require("react/jsx-runtime");
const _excluded = ["className", "ampm", "toolbarPlaceholder", "classes"];
const useUtilityClasses = (classes, ownerState) => {
  const {
    pickerVariant
  } = ownerState;
  const slots = {
    root: ['root'],
    container: ['container', pickerVariant],
    separator: ['separator'],
    timeContainer: ['timeContainer']
  };
  return (0, _composeClasses.default)(slots, _timeRangePickerToolbarClasses.getTimeRangePickerToolbarUtilityClass, classes);
};
const TimeRangePickerToolbarRoot = (0, _styles.styled)(_internals.PickersToolbar, {
  name: 'MuiTimeRangePickerToolbar',
  slot: 'Root'
})(({
  theme
}) => ({
  borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`,
  padding: '12px 0px 8px 0px',
  [`& .${_internals.pickersToolbarClasses.content} .${_internals.pickersToolbarTextClasses.root}[data-selected]`]: {
    color: (theme.vars || theme).palette.primary.main,
    fontWeight: theme.typography.fontWeightBold
  },
  [`& .${_internals.pickersToolbarClasses.title}`]: {
    paddingLeft: 12
  }
}));
const TimeRangePickerToolbarContainer = (0, _styles.styled)('div', {
  name: 'MuiTimeRangePickerToolbar',
  slot: 'Container',
  shouldForwardProp: prop => prop !== 'pickerVariant'
})({
  display: 'flex',
  flex: 1,
  variants: [{
    props: {
      pickerVariant: 'mobile'
    },
    style: {
      flexDirection: 'column',
      rowGap: 8
    }
  }, {
    props: {
      pickerVariant: 'desktop'
    },
    style: {
      flexDirection: 'row',
      gap: 1
    }
  }]
});
const TimeRangePickerToolbarTimeContainer = (0, _styles.styled)('div', {
  name: 'MuiTimeRangePickerToolbar',
  slot: 'TimeContainer'
})({
  display: 'flex',
  justifyContent: 'space-around',
  flex: 1
});
const TimeRangePickerToolbarSeparator = (0, _styles.styled)(_internals.PickersToolbarText, {
  name: 'MuiTimeRangePickerToolbar',
  slot: 'Separator'
})({
  cursor: 'default'
});
/**
 * @ignore - internal component
 */
function TimeRangePickerToolbarTimeElement(props) {
  const {
    value,
    ampm,
    onViewChange,
    view,
    separatorClasses,
    toolbarPlaceholder
  } = props;
  const adapter = (0, _hooks.usePickerAdapter)();
  const {
    variant,
    views
  } = (0, _hooks.usePickerContext)();
  const formatHours = time => ampm ? adapter.format(time, 'hours12h') : adapter.format(time, 'hours24h');
  const meridiemMode = (0, _internals.getMeridiem)(value, adapter);
  const sectionWidth = variant === 'desktop' ? _internals.MULTI_SECTION_CLOCK_SECTION_WIDTH : '100%';
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(TimeRangePickerToolbarTimeContainer, {
    children: [views.includes('hours') && /*#__PURE__*/(0, _jsxRuntime.jsxs)(React.Fragment, {
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_internals.PickersToolbarButton, {
        variant: "h5",
        width: sectionWidth,
        onClick: () => onViewChange('hours'),
        selected: view === 'hours',
        value: adapter.isValid(value) ? formatHours(value) : toolbarPlaceholder
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(TimeRangePickerToolbarSeparator, {
        variant: "h5",
        value: ":",
        className: separatorClasses
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_internals.PickersToolbarButton, {
        variant: "h5",
        width: sectionWidth,
        onClick: () => onViewChange('minutes'),
        selected: view === 'minutes' || !views.includes('minutes') && view === 'hours',
        value: adapter.isValid(value) ? adapter.format(value, 'minutes') : toolbarPlaceholder,
        disabled: !views.includes('minutes')
      })]
    }), views.includes('seconds') && /*#__PURE__*/(0, _jsxRuntime.jsxs)(React.Fragment, {
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(TimeRangePickerToolbarSeparator, {
        variant: "h5",
        value: ":",
        className: separatorClasses
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_internals.PickersToolbarButton, {
        variant: "h5",
        width: sectionWidth,
        onClick: () => onViewChange('seconds'),
        selected: view === 'seconds',
        value: value ? adapter.format(value, 'seconds') : toolbarPlaceholder
      })]
    }), ampm && /*#__PURE__*/(0, _jsxRuntime.jsx)(_internals.PickersToolbarButton, {
      variant: "h5",
      onClick: () => onViewChange('meridiem'),
      selected: view === 'meridiem',
      value: value && meridiemMode ? (0, _internals.formatMeridiem)(adapter, meridiemMode) : toolbarPlaceholder,
      width: sectionWidth
    })]
  });
}
process.env.NODE_ENV !== "production" ? TimeRangePickerToolbarTimeElement.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  ampm: _propTypes.default.bool.isRequired,
  onViewChange: _propTypes.default.func.isRequired,
  separatorClasses: _propTypes.default.string.isRequired,
  /**
   * Toolbar value placeholder—it is displayed when the value is empty.
   * @default "––"
   */
  toolbarPlaceholder: _propTypes.default.node,
  value: _propTypes.default.object,
  view: _propTypes.default.oneOf(['hours', 'meridiem', 'minutes', 'seconds'])
} : void 0;
const TimeRangePickerToolbar = exports.TimeRangePickerToolbar = /*#__PURE__*/React.forwardRef(function TimeRangePickerToolbar(inProps, ref) {
  const props = (0, _styles.useThemeProps)({
    props: inProps,
    name: 'MuiTimeRangePickerToolbar'
  });
  const {
      className,
      ampm,
      toolbarPlaceholder = '--',
      classes: classesProp
    } = props,
    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  const {
    value,
    view,
    setView
  } = (0, _hooks.usePickerContext)();
  const translations = (0, _hooks.usePickerTranslations)();
  const ownerState = (0, _internals.useToolbarOwnerState)();
  const {
    rangePosition,
    setRangePosition
  } = (0, _hooks2.usePickerRangePositionContext)();
  const classes = useUtilityClasses(classesProp, ownerState);
  const handleStartRangeViewChange = React.useCallback(newView => {
    if (rangePosition !== 'start') {
      setRangePosition('start');
    }
    setView(newView);
  }, [setRangePosition, setView, rangePosition]);
  const handleEndRangeViewChange = React.useCallback(newView => {
    if (rangePosition !== 'end') {
      setRangePosition('end');
    }
    setView(newView);
  }, [setRangePosition, setView, rangePosition]);
  if (!view) {
    return null;
  }
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(TimeRangePickerToolbarRoot, (0, _extends2.default)({}, other, {
    toolbarTitle: translations.timeRangePickerToolbarTitle,
    className: (0, _clsx.default)(className, classes.root),
    ownerState: ownerState,
    ref: ref,
    children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(TimeRangePickerToolbarContainer, {
      className: classes.container,
      pickerVariant: ownerState.pickerVariant,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(TimeRangePickerToolbarTimeElement, {
        view: rangePosition === 'start' ? view : undefined,
        value: value[0],
        onViewChange: handleStartRangeViewChange,
        ampm: ampm,
        separatorClasses: classes.separator,
        toolbarPlaceholder: toolbarPlaceholder
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(TimeRangePickerToolbarTimeElement, {
        view: rangePosition === 'end' ? view : undefined,
        value: value[1],
        onViewChange: handleEndRangeViewChange,
        ampm: ampm,
        separatorClasses: classes.separator,
        toolbarPlaceholder: toolbarPlaceholder
      })]
    })
  }));
});
if (process.env.NODE_ENV !== "production") TimeRangePickerToolbar.displayName = "TimeRangePickerToolbar";
process.env.NODE_ENV !== "production" ? TimeRangePickerToolbar.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  ampm: _propTypes.default.bool.isRequired,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: _propTypes.default.object,
  className: _propTypes.default.string,
  /**
   * If `true`, show the toolbar even in desktop mode.
   * @default `true` for Desktop, `false` for Mobile.
   */
  hidden: _propTypes.default.bool,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object]),
  titleId: _propTypes.default.string,
  /**
   * Toolbar value placeholder—it is displayed when the value is empty.
   * @default "––"
   */
  toolbarPlaceholder: _propTypes.default.node
} : void 0;