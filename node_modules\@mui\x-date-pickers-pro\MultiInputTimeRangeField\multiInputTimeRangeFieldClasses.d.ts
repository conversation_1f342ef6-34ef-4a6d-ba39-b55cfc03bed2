import { MultiInputRangeFieldClasses } from "../internals/utils/createMultiInputRangeField/index.js";
export interface MultiInputTimeRangeFieldClasses extends MultiInputRangeFieldClasses {}
export type MultiInputTimeRangeFieldClassKey = keyof MultiInputRangeFieldClasses;
export declare const multiInputTimeRangeFieldClasses: MultiInputRangeFieldClasses;
export declare const getMultiInputTimeRangeFieldUtilityClass: (slot: string) => string;