import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  InputAdornment,
} from '@mui/material';
import { ApartmentWithDetails, CreateApartmentRequest, UpdateApartmentRequest } from '../../../types/Apartment';
import { ApartmentService } from '../../services/apartmentService';

interface ApartmentFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: CreateApartmentRequest | UpdateApartmentRequest) => Promise<void>;
  apartment?: ApartmentWithDetails; // For edit mode
  floorId: number; // Required for creating new apartments
  floorNumber: number; // For auto-generating apartment names
  loading?: boolean;
}

interface FormData {
  name: string;
  currentRate: string;
  size: string;
  numberOfRooms: string;
}

const ApartmentForm: React.FC<ApartmentFormProps> = ({
  open,
  onClose,
  onSubmit,
  apartment,
  floorId,
  floorNumber,
  loading = false,
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    currentRate: '',
    size: '',
    numberOfRooms: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [nextApartmentPosition, setNextApartmentPosition] = useState(1);

  const isEditMode = !!apartment;

  // Initialize form data when apartment prop changes
  useEffect(() => {
    if (apartment) {
      setFormData({
        name: apartment.name,
        currentRate: apartment.currentRate.toString(),
        size: apartment.size,
        numberOfRooms: apartment.numberOfRooms.toString(),
      });
    } else {
      // Auto-generate apartment name for new apartments
      const generatedName = ApartmentService.generateApartmentName(floorNumber, nextApartmentPosition);
      setFormData({
        name: generatedName,
        currentRate: '',
        size: '',
        numberOfRooms: '1',
      });
    }
    setErrors({});
  }, [apartment, open, floorNumber, nextApartmentPosition]);

  const handleInputChange = (field: keyof FormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const value = event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleNumberInputChange = (field: keyof FormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value;
    // Only allow positive numbers
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setFormData(prev => ({ ...prev, [field]: value }));
      
      // Clear error when user starts typing
      if (errors[field]) {
        setErrors(prev => ({ ...prev, [field]: '' }));
      }
    }
  };

  const handleRoomsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    // Only allow positive integers
    if (value === '' || /^\d+$/.test(value)) {
      setFormData(prev => ({ ...prev, numberOfRooms: value }));
      
      // Clear error when user starts typing
      if (errors.numberOfRooms) {
        setErrors(prev => ({ ...prev, numberOfRooms: '' }));
      }
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate name
    if (!formData.name.trim()) {
      newErrors.name = 'Apartment name is required';
    } else if (formData.name.trim().length > 100) {
      newErrors.name = 'Apartment name must be less than 100 characters';
    }

    // Validate current rate
    if (!formData.currentRate.trim()) {
      newErrors.currentRate = 'Current rate is required';
    } else {
      const rate = parseFloat(formData.currentRate);
      if (isNaN(rate) || rate <= 0) {
        newErrors.currentRate = 'Current rate must be a valid positive number';
      } else if (rate > 1000000) {
        newErrors.currentRate = 'Current rate cannot exceed 1,000,000';
      }
    }

    // Validate size
    if (!formData.size.trim()) {
      newErrors.size = 'Apartment size is required';
    } else if (formData.size.trim().length > 50) {
      newErrors.size = 'Apartment size must be less than 50 characters';
    }

    // Validate number of rooms
    if (!formData.numberOfRooms.trim()) {
      newErrors.numberOfRooms = 'Number of rooms is required';
    } else {
      const rooms = parseInt(formData.numberOfRooms);
      if (isNaN(rooms) || rooms <= 0) {
        newErrors.numberOfRooms = 'Number of rooms must be a valid positive number';
      } else if (rooms > 20) {
        newErrors.numberOfRooms = 'Number of rooms cannot exceed 20';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const submitData = isEditMode
        ? {
            id: apartment!.id,
            name: formData.name.trim(),
            currentRate: parseFloat(formData.currentRate),
            size: formData.size.trim(),
            numberOfRooms: parseInt(formData.numberOfRooms),
          } as UpdateApartmentRequest
        : {
            name: formData.name.trim(),
            currentRate: parseFloat(formData.currentRate),
            size: formData.size.trim(),
            numberOfRooms: parseInt(formData.numberOfRooms),
            floorId,
          } as CreateApartmentRequest;

      await onSubmit(submitData);
    } catch (error) {
      console.error('Error submitting apartment form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      currentRate: '',
      size: '',
      numberOfRooms: '',
    });
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  const getRoomOptions = () => {
    const options = [];
    for (let i = 1; i <= 10; i++) {
      options.push(
        <MenuItem key={i} value={i.toString()}>
          {i} {i === 1 ? 'Room' : 'Rooms'}
        </MenuItem>
      );
    }
    return options;
  };

  const getSizeOptions = () => {
    const commonSizes = [
      '500 sq ft',
      '600 sq ft',
      '700 sq ft',
      '800 sq ft',
      '900 sq ft',
      '1000 sq ft',
      '1200 sq ft',
      '1500 sq ft',
      '2000 sq ft',
    ];

    return commonSizes.map(size => (
      <MenuItem key={size} value={size}>
        {size}
      </MenuItem>
    ));
  };

  const generateNewName = () => {
    const newName = ApartmentService.generateApartmentName(floorNumber, nextApartmentPosition);
    setFormData(prev => ({ ...prev, name: newName }));
    setNextApartmentPosition(prev => prev + 1);
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
          {isEditMode ? 'Edit Apartment' : 'Create New Apartment'}
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <Grid container spacing={3}>
            {/* Apartment Name */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Apartment Name"
                value={formData.name}
                onChange={handleInputChange('name')}
                error={!!errors.name}
                helperText={errors.name}
                placeholder="e.g., G01, 101, 201"
              />
              {!isEditMode && (
                <Button
                  size="small"
                  onClick={generateNewName}
                  sx={{ mt: 1 }}
                >
                  Generate Name
                </Button>
              )}
            </Grid>

            {/* Current Rate */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Monthly Rent"
                value={formData.currentRate}
                onChange={handleNumberInputChange('currentRate')}
                error={!!errors.currentRate}
                helperText={errors.currentRate}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
                placeholder="1200"
              />
            </Grid>

            {/* Size */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.size}>
                <InputLabel>Apartment Size</InputLabel>
                <Select
                  value={formData.size}
                  label="Apartment Size"
                  onChange={(e) => setFormData(prev => ({ ...prev, size: e.target.value }))}
                >
                  {getSizeOptions()}
                  <MenuItem value="custom">
                    <em>Custom Size</em>
                  </MenuItem>
                </Select>
                {errors.size && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                    {errors.size}
                  </Typography>
                )}
              </FormControl>
              {formData.size === 'custom' && (
                <TextField
                  fullWidth
                  label="Custom Size"
                  value={formData.size === 'custom' ? '' : formData.size}
                  onChange={handleInputChange('size')}
                  placeholder="e.g., 850 sq ft"
                  sx={{ mt: 2 }}
                />
              )}
            </Grid>

            {/* Number of Rooms */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.numberOfRooms}>
                <InputLabel>Number of Rooms</InputLabel>
                <Select
                  value={formData.numberOfRooms}
                  label="Number of Rooms"
                  onChange={(e) => setFormData(prev => ({ ...prev, numberOfRooms: e.target.value }))}
                >
                  {getRoomOptions()}
                </Select>
                {errors.numberOfRooms && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                    {errors.numberOfRooms}
                  </Typography>
                )}
              </FormControl>
            </Grid>

            {/* Info Alert */}
            <Grid item xs={12}>
              <Alert severity="info" sx={{ mt: 1 }}>
                {isEditMode
                  ? 'Update apartment details. Note that changing the rent will not affect existing bookings.'
                  : 'Create a new apartment. The apartment will be available for booking once created.'}
              </Alert>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={isSubmitting || loading}
          startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
        >
          {isSubmitting ? 'Saving...' : isEditMode ? 'Update Apartment' : 'Create Apartment'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ApartmentForm;
