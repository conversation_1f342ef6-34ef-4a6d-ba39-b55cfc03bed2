"use strict";
'use client';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useMultiInputRangeField = useMultiInputRangeField;
var _internals = require("@mui/x-date-pickers/internals");
var _validation = require("@mui/x-date-pickers/validation");
var _useTextFieldProps = require("./useTextFieldProps");
var _useMultiInputRangeFieldSelectedSections = require("./useMultiInputRangeFieldSelectedSections");
var _useMultiInputRangeFieldRootProps = require("./useMultiInputRangeFieldRootProps");
/**
 * Basic example:
 *
 * ```tsx
 * import Box from '@mui/material/Box';
 * import { useSplitFieldProps } from '@mui/x-date-pickers/hooks';
 * import { PickersTextField } from '@mui/x-date-pickers/PickersTextField';
 * import { useDateRangeManager } from '@mui/x-date-pickers-pro/managers';
 *
 * function MultiInputField(props) {
 *   const manager = useDateRangeManager();
 *   const { internalProps, forwardedProps } = useSplitFieldProps(props, 'date');
 *   const response = useMultiInputRangeField({
 *     manager,
 *     internalProps,
 *     startTextFieldProps: {},
 *     endTextFieldProps: {},
 *     rootProps: forwardedProps,
 *   });
 *
 *   return (
 *     <Box {...response.root}>
 *       <PickersTextField {...response.startTextField} />
 *       <span>{' – '}</span>
 *       <PickersTextField {...response.endTextField} />
 *     </Box>
 *   );
 * }
 * ```
 *
 * @param {UseMultiInputRangeFieldParameters<TManager, TTextFieldProps>} parameters The parameters of the hook.
 * @param {TManager} parameters.manager The manager of the field.
 * @param {PickerManagerFieldInternalProps<TManager>} parameters.internalProps The internal props of the field.
 * @param {TTextFieldProps} parameters.startForwardedProps The forwarded props of the start field.
 * @param {TTextFieldProps} parameters.endForwardedProps The forwarded props of the end field.
 * @returns {UseMultiInputRangeFieldReturnValue<TManager, TTextFieldProps>} The props to pass to the start and the end components.
 */
function useMultiInputRangeField(parameters) {
  const {
    manager,
    internalProps,
    rootProps,
    startTextFieldProps,
    endTextFieldProps
  } = parameters;
  const internalPropsWithDefaults = (0, _internals.useFieldInternalPropsWithDefaults)({
    manager,
    internalProps
  });
  const {
    value: valueProp,
    defaultValue,
    format,
    formatDensity,
    shouldRespectLeadingZeros,
    onChange,
    disabled,
    readOnly,
    selectedSections,
    onSelectedSectionsChange,
    timezone: timezoneProp,
    enableAccessibleFieldDOMStructure,
    autoFocus,
    referenceDate,
    unstableStartFieldRef,
    unstableEndFieldRef
  } = internalPropsWithDefaults;
  const {
    value,
    handleValueChange,
    timezone
  } = (0, _internals.useControlledValue)({
    name: 'useMultiInputRangeField',
    timezone: timezoneProp,
    value: valueProp,
    defaultValue,
    referenceDate,
    onChange,
    valueManager: manager.internal_valueManager
  });
  const validation = (0, _validation.useValidation)({
    props: internalPropsWithDefaults,
    value,
    timezone,
    validator: manager.validator,
    onError: internalPropsWithDefaults.onError
  });
  const selectedSectionsResponse = (0, _useMultiInputRangeFieldSelectedSections.useMultiInputRangeFieldSelectedSections)({
    selectedSections,
    onSelectedSectionsChange,
    unstableStartFieldRef,
    unstableEndFieldRef
  });
  const sharedInternalProps = {
    disabled,
    readOnly,
    timezone,
    format,
    formatDensity,
    shouldRespectLeadingZeros,
    enableAccessibleFieldDOMStructure
  };
  const rootResponse = (0, _useMultiInputRangeFieldRootProps.useMultiInputRangeFieldRootProps)(rootProps);
  const startTextFieldResponse = (0, _useTextFieldProps.useTextFieldProps)({
    valueType: manager.valueType,
    position: 'start',
    value,
    onChange: handleValueChange,
    autoFocus,
    validation,
    forwardedProps: startTextFieldProps,
    selectedSectionProps: selectedSectionsResponse.start,
    sharedInternalProps
  });
  const endTextFieldResponse = (0, _useTextFieldProps.useTextFieldProps)({
    valueType: manager.valueType,
    position: 'end',
    value,
    onChange: handleValueChange,
    autoFocus,
    validation,
    forwardedProps: endTextFieldProps,
    selectedSectionProps: selectedSectionsResponse.end,
    sharedInternalProps
  });
  return {
    root: rootResponse,
    startTextField: startTextFieldResponse,
    endTextField: endTextFieldResponse,
    enableAccessibleFieldDOMStructure
  };
}