import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  InputAdornment,
  Autocomplete,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { ApartmentWithDetails, BookApartmentRequest } from '../../../types/Apartment';
import { Customer } from '../../../types/Customer';
import { ApartmentService } from '../../services/apartmentService';

interface BookingFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: BookApartmentRequest) => Promise<void>;
  apartment: ApartmentWithDetails;
  customers: Customer[]; // Available customers for booking
  loading?: boolean;
}

interface FormData {
  customerId: string;
  startDate: Dayjs | null;
  rentAmount: string;
  advanceAmount: string;
}

const BookingForm: React.FC<BookingFormProps> = ({
  open,
  onClose,
  onSubmit,
  apartment,
  customers,
  loading = false,
}) => {
  const [formData, setFormData] = useState<FormData>({
    customerId: '',
    startDate: dayjs(),
    rentAmount: apartment.currentRate.toString(),
    advanceAmount: (apartment.currentRate * 2).toString(), // Default to 2 months advance
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  // Initialize form data when dialog opens
  useEffect(() => {
    if (open) {
      setFormData({
        customerId: '',
        startDate: dayjs(),
        rentAmount: apartment.currentRate.toString(),
        advanceAmount: (apartment.currentRate * 2).toString(),
      });
      setSelectedCustomer(null);
      setErrors({});
    }
  }, [open, apartment.currentRate]);

  const handleInputChange = (field: keyof FormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const value = event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleNumberInputChange = (field: keyof FormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value;
    // Only allow positive numbers
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setFormData(prev => ({ ...prev, [field]: value }));
      
      // Clear error when user starts typing
      if (errors[field]) {
        setErrors(prev => ({ ...prev, [field]: '' }));
      }
    }
  };

  const handleCustomerChange = (customer: Customer | null) => {
    setSelectedCustomer(customer);
    setFormData(prev => ({ 
      ...prev, 
      customerId: customer ? customer.id.toString() : '' 
    }));
    
    // Clear error when customer is selected
    if (errors.customerId) {
      setErrors(prev => ({ ...prev, customerId: '' }));
    }
  };

  const handleDateChange = (date: Dayjs | null) => {
    setFormData(prev => ({ ...prev, startDate: date }));
    
    // Clear error when date is selected
    if (errors.startDate) {
      setErrors(prev => ({ ...prev, startDate: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate customer selection
    if (!formData.customerId) {
      newErrors.customerId = 'Please select a customer';
    }

    // Validate start date
    if (!formData.startDate) {
      newErrors.startDate = 'Start date is required';
    } else if (formData.startDate.isBefore(dayjs(), 'day')) {
      newErrors.startDate = 'Start date cannot be in the past';
    }

    // Validate rent amount
    if (!formData.rentAmount.trim()) {
      newErrors.rentAmount = 'Rent amount is required';
    } else {
      const rent = parseFloat(formData.rentAmount);
      if (isNaN(rent) || rent <= 0) {
        newErrors.rentAmount = 'Rent amount must be a valid positive number';
      } else if (rent > 1000000) {
        newErrors.rentAmount = 'Rent amount cannot exceed 1,000,000';
      }
    }

    // Validate advance amount
    if (!formData.advanceAmount.trim()) {
      newErrors.advanceAmount = 'Advance amount is required';
    } else {
      const advance = parseFloat(formData.advanceAmount);
      if (isNaN(advance) || advance < 0) {
        newErrors.advanceAmount = 'Advance amount must be a valid positive number';
      } else if (advance > 1000000) {
        newErrors.advanceAmount = 'Advance amount cannot exceed 1,000,000';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const submitData: BookApartmentRequest = {
        apartmentId: apartment.id,
        customerId: parseInt(formData.customerId),
        startDate: formData.startDate!.toDate(),
        rentAmount: parseFloat(formData.rentAmount),
        advanceAmount: parseFloat(formData.advanceAmount),
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error('Error submitting booking form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      customerId: '',
      startDate: dayjs(),
      rentAmount: apartment.currentRate.toString(),
      advanceAmount: (apartment.currentRate * 2).toString(),
    });
    setSelectedCustomer(null);
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  const availableCustomers = customers.filter(customer => customer.isActive === false || !customer.isActive);

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
          Book Apartment - {apartment.name}
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ pt: 2 }}>
          {/* Apartment Info */}
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>Apartment:</strong> {apartment.name} ({apartment.size}, {apartment.numberOfRooms} rooms)
              <br />
              <strong>Current Rate:</strong> {ApartmentService.formatCurrency(apartment.currentRate)}/month
            </Typography>
          </Alert>

          <Grid container spacing={3}>
            {/* Customer Selection */}
            <Grid item xs={12}>
              <Autocomplete
                options={availableCustomers}
                getOptionLabel={(customer) => `${customer.name} - ${customer.mobile}`}
                value={selectedCustomer}
                onChange={(_, newValue) => handleCustomerChange(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Customer"
                    error={!!errors.customerId}
                    helperText={errors.customerId || 'Choose a customer to book this apartment'}
                    fullWidth
                  />
                )}
                renderOption={(props, customer) => (
                  <Box component="li" {...props}>
                    <Box>
                      <Typography variant="body1">{customer.name}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {customer.mobile} • {customer.occupation}
                      </Typography>
                    </Box>
                  </Box>
                )}
              />
            </Grid>

            {/* Start Date */}
            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Start Date"
                value={formData.startDate}
                onChange={handleDateChange}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.startDate,
                    helperText: errors.startDate,
                  },
                }}
                minDate={dayjs()}
              />
            </Grid>

            {/* Rent Amount */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Monthly Rent"
                value={formData.rentAmount}
                onChange={handleNumberInputChange('rentAmount')}
                error={!!errors.rentAmount}
                helperText={errors.rentAmount}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
              />
            </Grid>

            {/* Advance Amount */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Advance Amount"
                value={formData.advanceAmount}
                onChange={handleNumberInputChange('advanceAmount')}
                error={!!errors.advanceAmount}
                helperText={errors.advanceAmount || 'Security deposit/advance payment'}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
              />
            </Grid>

            {/* Quick Advance Options */}
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Quick Advance Options:
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => setFormData(prev => ({ 
                    ...prev, 
                    advanceAmount: prev.rentAmount 
                  }))}
                >
                  1 Month
                </Button>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => setFormData(prev => ({ 
                    ...prev, 
                    advanceAmount: (parseFloat(prev.rentAmount) * 2).toString() 
                  }))}
                >
                  2 Months
                </Button>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => setFormData(prev => ({ 
                    ...prev, 
                    advanceAmount: (parseFloat(prev.rentAmount) * 3).toString() 
                  }))}
                >
                  3 Months
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={isSubmitting || loading}
          startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
        >
          {isSubmitting ? 'Booking...' : 'Book Apartment'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BookingForm;
