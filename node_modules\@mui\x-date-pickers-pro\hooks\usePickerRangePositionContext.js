"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PickerRangePositionContext = void 0;
exports.usePickerRangePositionContext = usePickerRangePositionContext;
var React = _interopRequireWildcard(require("react"));
const PickerRangePositionContext = exports.PickerRangePositionContext = /*#__PURE__*/React.createContext(null);

/**
 * Returns information about the range position of the picker that wraps the current component.
 */
if (process.env.NODE_ENV !== "production") PickerRangePositionContext.displayName = "PickerRangePositionContext";
function usePickerRangePositionContext() {
  const value = React.useContext(PickerRangePositionContext);
  if (value == null) {
    throw new Error(['MUI X: The `usePickerRangePositionContext` can only be called in components that are used inside a picker component'].join('\n'));
  }
  return value;
}