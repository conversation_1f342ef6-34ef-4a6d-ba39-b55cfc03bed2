import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Button,
  Grid,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  InputAdornment,
  Fab,
  Tooltip,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  People as PeopleIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { CustomerWithCurrentApartment, CreateCustomerRequest, UpdateCustomerRequest } from '../../types/Customer';
import { CustomerService } from '../services/customerService';
import { useNotification } from '../contexts/NotificationContext';
import CustomerForm from '../components/forms/CustomerForm';
import CustomerCard from '../components/cards/CustomerCard';

const CustomersPage: React.FC = () => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();

  const [customers, setCustomers] = useState<CustomerWithCurrentApartment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [formOpen, setFormOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<CustomerWithCurrentApartment | undefined>();
  const [formLoading, setFormLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<CustomerWithCurrentApartment | null>(null);

  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = async () => {
    try {
      setLoading(true);

      // For now, use mock data since we don't have the backend implemented yet
      const mockCustomers: CustomerWithCurrentApartment[] = [
        {
          id: 1,
          name: 'Alice Johnson',
          address: '789 Oak Street, Downtown',
          mobile: '******-0789',
          occupation: 'Software Engineer',
          startDate: new Date('2023-06-01'),
          rent: 1200,
          advance: 2400,
          isActive: true,
          nidPath: '/uploads/customers/1/nid.jpg',
          photoPath: '/uploads/customers/1/photo.jpg',
          createdAt: new Date('2023-05-25'),
          updatedAt: new Date('2023-06-01'),
          currentApartment: {
            id: 1,
            name: 'G01',
            currentRate: 1200,
            size: '800 sq ft',
            numberOfRooms: 2,
            floorId: 1,
            isOccupied: true,
            createdAt: new Date(),
            updatedAt: new Date(),
            floor: {
              id: 1,
              name: 'Ground Floor',
              floorNumber: 0,
              buildingId: 1,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
            building: {
              id: 1,
              name: 'Sunset Apartments',
              address: '123 Main Street, Downtown',
              ownerName: 'John Smith',
              ownerMobileNo: '******-0123',
              ownerAddress: '456 Oak Avenue',
              rentAmount: 1200,
              advance: 2400,
              numberOfFloor: 3,
              agreementDate: new Date('2023-01-15'),
              handOverFromOwner: new Date('2023-02-01'),
              handOverToOwner: undefined,
              conditions: 'No pets allowed',
              comments: 'Well-maintained building',
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          },
        },
        {
          id: 2,
          name: 'Bob Wilson',
          address: '456 Pine Street, Suburbs',
          mobile: '******-0456',
          occupation: 'Teacher',
          startDate: new Date('2023-02-01'),
          rent: 1100,
          advance: 2200,
          isActive: false,
          nidPath: '/uploads/customers/2/nid.pdf',
          createdAt: new Date('2023-01-25'),
          updatedAt: new Date('2023-05-31'),
        },
        {
          id: 3,
          name: 'Carol Davis',
          address: '321 Elm Street, Midtown',
          mobile: '******-0321',
          occupation: 'Marketing Manager',
          startDate: new Date('2023-07-15'),
          rent: 1300,
          advance: 2600,
          isActive: true,
          photoPath: '/uploads/customers/3/photo.jpg',
          createdAt: new Date('2023-07-10'),
          updatedAt: new Date('2023-07-15'),
          currentApartment: {
            id: 3,
            name: '203',
            currentRate: 1300,
            size: '900 sq ft',
            numberOfRooms: 3,
            floorId: 3,
            isOccupied: true,
            createdAt: new Date(),
            updatedAt: new Date(),
            floor: {
              id: 3,
              name: '2nd Floor',
              floorNumber: 2,
              buildingId: 1,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
            building: {
              id: 1,
              name: 'Sunset Apartments',
              address: '123 Main Street, Downtown',
              ownerName: 'John Smith',
              ownerMobileNo: '******-0123',
              ownerAddress: '456 Oak Avenue',
              rentAmount: 1200,
              advance: 2400,
              numberOfFloor: 3,
              agreementDate: new Date('2023-01-15'),
              handOverFromOwner: new Date('2023-02-01'),
              handOverToOwner: undefined,
              conditions: 'No pets allowed',
              comments: 'Well-maintained building',
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          },
        },
        {
          id: 4,
          name: 'David Brown',
          address: '654 Maple Avenue, Uptown',
          mobile: '******-0654',
          occupation: 'Accountant',
          startDate: new Date('2023-03-01'),
          rent: 1150,
          advance: 2300,
          isActive: false,
          createdAt: new Date('2023-02-25'),
          updatedAt: new Date('2023-06-30'),
        },
      ];

      setCustomers(CustomerService.sortCustomersByName(mockCustomers));
    } catch (error) {
      showError('Failed to load customers');
      console.error('Error loading customers:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCustomer = () => {
    setEditingCustomer(undefined);
    setFormOpen(true);
  };

  const handleEditCustomer = (customer: CustomerWithCurrentApartment) => {
    setEditingCustomer(customer);
    setFormOpen(true);
  };

  const handleDeleteCustomer = (customer: CustomerWithCurrentApartment) => {
    setCustomerToDelete(customer);
    setDeleteDialogOpen(true);
  };

  const handleViewCustomer = (customer: CustomerWithCurrentApartment) => {
    console.log('Viewing customer:', customer.name);
  };

  const handleFormSubmit = async (data: CreateCustomerRequest | UpdateCustomerRequest) => {
    try {
      setFormLoading(true);

      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      if ('id' in data) {
        showSuccess('Customer updated successfully');
      } else {
        showSuccess('Customer created successfully');
      }

      setFormOpen(false);
      await loadCustomers();
    } catch (error) {
      showError('Failed to save customer');
      console.error('Error saving customer:', error);
    } finally {
      setFormLoading(false);
    }
  };

  const confirmDelete = async () => {
    if (!customerToDelete) return;

    try {
      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      showSuccess('Customer deleted successfully');
      setDeleteDialogOpen(false);
      setCustomerToDelete(null);
      await loadCustomers();
    } catch (error) {
      showError('Failed to delete customer');
      console.error('Error deleting customer:', error);
    }
  };

  const handleExport = () => {
    try {
      const exportData = CustomerService.exportCustomersData(customers);
      const dataStr = JSON.stringify(exportData, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

      const exportFileDefaultName = `customers_${new Date().toISOString().split('T')[0]}.json`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();

      showSuccess('Customer data exported successfully');
    } catch (error) {
      showError('Failed to export customer data');
      console.error('Error exporting data:', error);
    }
  };

  const filteredCustomers = CustomerService.filterCustomersByStatus(
    CustomerService.filterCustomers(customers, searchTerm),
    statusFilter
  );

  const customerStats = CustomerService.calculateCustomerStats(customers);

  return (
    <Container maxWidth="xl">
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
              Customer Management
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage your customers and their rental information
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Tooltip title="Export Data">
              <Button
                variant="outlined"
                startIcon={<ExportIcon />}
                onClick={handleExport}
                disabled={loading || customers.length === 0}
              >
                Export
              </Button>
            </Tooltip>
            <Tooltip title="Refresh">
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={loadCustomers}
                disabled={loading}
              >
                Refresh
              </Button>
            </Tooltip>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateCustomer}
              size="large"
            >
              Add Customer
            </Button>
          </Box>
        </Box>

        {/* Statistics Cards */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary" sx={{ fontWeight: 600 }}>
                  {customerStats.totalCustomers}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Customers
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                  {customerStats.activeCustomers}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Active Tenants
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main" sx={{ fontWeight: 600 }}>
                  {customerStats.inactiveCustomers}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Inactive
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="secondary.main" sx={{ fontWeight: 600 }}>
                  {CustomerService.formatCurrency(customerStats.totalRentCollection)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Monthly Collection
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Search and Filter */}
        <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
          <TextField
            placeholder="Search customers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 300, flexGrow: 1 }}
          />

          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Status Filter</InputLabel>
            <Select
              value={statusFilter}
              label="Status Filter"
              onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
              startAdornment={<FilterIcon sx={{ mr: 1 }} />}
            >
              <MenuItem value="all">All Customers</MenuItem>
              <MenuItem value="active">Active Only</MenuItem>
              <MenuItem value="inactive">Inactive Only</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>

      {/* Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      ) : customers.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          No customers found. Click "Add Customer" to create your first customer profile.
        </Alert>
      ) : (
        <>
          {/* Customers Grid */}
          <Grid container spacing={3}>
            {filteredCustomers.map((customer) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={customer.id}>
                <CustomerCard
                  customer={customer}
                  onEdit={handleEditCustomer}
                  onDelete={handleDeleteCustomer}
                  onView={handleViewCustomer}
                />
              </Grid>
            ))}
          </Grid>

          {/* No Results Message */}
          {filteredCustomers.length === 0 && (
            <Alert severity="info" sx={{ mt: 3 }}>
              No customers match your current search and filter criteria.
            </Alert>
          )}
        </>
      )}

      {/* Floating Action Button for Mobile */}
      <Fab
        color="primary"
        aria-label="add customer"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          display: { xs: 'flex', md: 'none' },
        }}
        onClick={handleCreateCustomer}
      >
        <AddIcon />
      </Fab>

      {/* Customer Form Dialog */}
      <CustomerForm
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        customer={editingCustomer}
        loading={formLoading}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Customer</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete customer "{customerToDelete?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default CustomersPage;
