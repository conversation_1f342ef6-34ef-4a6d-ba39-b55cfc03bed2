import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
  Divider,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Breadcrumbs,
  Link,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Business as BusinessIcon,
  Layers as LayersIcon,
  Home as HomeIcon,
  Person as PersonIcon,
  AttachMoney as MoneyIcon,
  CalendarToday as CalendarIcon,
  History as HistoryIcon,
  NavigateNext as NavigateNextIcon,
  CheckCircle as OccupiedIcon,
  RadioButtonUnchecked as AvailableIcon,
  ExitToApp as CheckoutIcon,
  PersonAdd as BookIcon,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { ApartmentWithDetails, ApartmentBookingHistory } from '../../types/Apartment';
import { ApartmentService } from '../services/apartmentService';
import { useNotification } from '../contexts/NotificationContext';

const ApartmentDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();

  const [apartment, setApartment] = useState<ApartmentWithDetails | null>(null);
  const [bookingHistory, setBookingHistory] = useState<ApartmentBookingHistory[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadApartmentDetails();
    }
  }, [id]);

  const loadApartmentDetails = async () => {
    try {
      setLoading(true);

      // For now, use mock data since we don't have the backend implemented yet
      const mockApartment: ApartmentWithDetails = {
        id: parseInt(id!),
        name: 'G01',
        currentRate: 1200,
        size: '800 sq ft',
        numberOfRooms: 2,
        floorId: 1,
        currentCustomerId: 1,
        isOccupied: true,
        createdAt: new Date('2023-01-15'),
        updatedAt: new Date('2023-08-01'),
        floor: {
          id: 1,
          name: 'Ground Floor',
          floorNumber: 0,
          buildingId: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        building: {
          id: 1,
          name: 'Sunset Apartments',
          address: '123 Main Street, Downtown',
          ownerName: 'John Smith',
          ownerMobileNo: '******-0123',
          ownerAddress: '456 Oak Avenue',
          rentAmount: 1200,
          advance: 2400,
          numberOfFloor: 3,
          agreementDate: new Date('2023-01-15'),
          handOverFromOwner: new Date('2023-02-01'),
          handOverToOwner: undefined,
          conditions: 'No pets allowed',
          comments: 'Well-maintained building',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        currentCustomer: {
          id: 1,
          name: 'Alice Johnson',
          address: '789 Oak Street',
          mobile: '******-0789',
          occupation: 'Software Engineer',
          startDate: new Date('2023-06-01'),
          rent: 1200,
          advance: 2400,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      };

      const mockBookingHistory: ApartmentBookingHistory[] = [
        {
          id: 1,
          apartmentId: parseInt(id!),
          customerId: 1,
          customer: mockApartment.currentCustomer!,
          startDate: new Date('2023-06-01'),
          endDate: undefined, // Current booking
          rentAmount: 1200,
          advanceAmount: 2400,
          createdAt: new Date('2023-05-25'),
          updatedAt: new Date('2023-06-01'),
        },
        {
          id: 2,
          apartmentId: parseInt(id!),
          customerId: 2,
          customer: {
            id: 2,
            name: 'Bob Wilson',
            address: '456 Pine Street',
            mobile: '******-0456',
            occupation: 'Teacher',
            startDate: new Date('2023-02-01'),
            rent: 1100,
            advance: 2200,
            isActive: false,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          startDate: new Date('2023-02-01'),
          endDate: new Date('2023-05-31'),
          rentAmount: 1100,
          advanceAmount: 2200,
          checkoutReason: 'Lease expired',
          createdAt: new Date('2023-01-25'),
          updatedAt: new Date('2023-05-31'),
        },
      ];

      setApartment(mockApartment);
      setBookingHistory(mockBookingHistory);
    } catch (error) {
      showError('Failed to load apartment details');
      console.error('Error loading apartment details:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    // TODO: Implement edit functionality
    showSuccess('Edit functionality will be implemented');
  };

  const handleDelete = () => {
    // TODO: Implement delete functionality
    showSuccess('Delete functionality will be implemented');
  };

  const handleBook = () => {
    // TODO: Implement booking functionality
    showSuccess('Booking functionality will be implemented');
  };

  const handleCheckout = () => {
    // TODO: Implement checkout functionality
    showSuccess('Checkout functionality will be implemented');
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number): string => {
    return ApartmentService.formatCurrency(amount);
  };

  if (!id) {
    return (
      <Container maxWidth="xl">
        <Alert severity="error">Apartment ID is required</Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (!apartment) {
    return (
      <Container maxWidth="xl">
        <Alert severity="error">Apartment not found</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      {/* Breadcrumbs */}
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs separator={<NavigateNextIcon fontSize="small" />}>
          <Link
            color="inherit"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              navigate('/buildings');
            }}
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Buildings
          </Link>
          <Link
            color="inherit"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              navigate(`/buildings/${apartment.building.id}/floors`);
            }}
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <LayersIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            {apartment.building.name}
          </Link>
          <Link
            color="inherit"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              navigate(`/floors/${apartment.floor.id}/apartments`);
            }}
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            {apartment.floor.name}
          </Link>
          <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            {apartment.name}
          </Typography>
        </Breadcrumbs>
      </Box>

      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
              Apartment {apartment.name}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {apartment.floor.name} • {apartment.building.name}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<EditIcon />}
              onClick={handleEdit}
            >
              Edit
            </Button>
            {apartment.isOccupied ? (
              <Button
                variant="contained"
                color="warning"
                startIcon={<CheckoutIcon />}
                onClick={handleCheckout}
              >
                Checkout
              </Button>
            ) : (
              <Button
                variant="contained"
                color="success"
                startIcon={<BookIcon />}
                onClick={handleBook}
              >
                Book Apartment
              </Button>
            )}
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleDelete}
            >
              Delete
            </Button>
          </Box>
        </Box>

        {/* Status Chip */}
        <Chip
          icon={apartment.isOccupied ? <OccupiedIcon /> : <AvailableIcon />}
          label={apartment.isOccupied ? 'Occupied' : 'Available'}
          color={apartment.isOccupied ? 'success' : 'default'}
          size="medium"
          sx={{ fontWeight: 600 }}
        />
      </Box>

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Apartment Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Apartment Information
              </Typography>
              <List>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <HomeIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Apartment Name"
                    secondary={apartment.name}
                  />
                </ListItem>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'secondary.main' }}>
                      <MoneyIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Monthly Rent"
                    secondary={formatCurrency(apartment.currentRate)}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Size"
                    secondary={apartment.size}
                    sx={{ pl: 7 }}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Number of Rooms"
                    secondary={`${apartment.numberOfRooms} ${apartment.numberOfRooms === 1 ? 'Room' : 'Rooms'}`}
                    sx={{ pl: 7 }}
                  />
                </ListItem>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'info.main' }}>
                      <CalendarIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Created"
                    secondary={formatDate(apartment.createdAt)}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Current Customer Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Current Tenant
              </Typography>
              {apartment.isOccupied && apartment.currentCustomer ? (
                <List>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'success.main' }}>
                        <PersonIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary="Name"
                      secondary={apartment.currentCustomer.name}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Mobile"
                      secondary={apartment.currentCustomer.mobile}
                      sx={{ pl: 7 }}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Occupation"
                      secondary={apartment.currentCustomer.occupation}
                      sx={{ pl: 7 }}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Address"
                      secondary={apartment.currentCustomer.address}
                      sx={{ pl: 7 }}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Start Date"
                      secondary={formatDate(apartment.currentCustomer.startDate)}
                      sx={{ pl: 7 }}
                    />
                  </ListItem>
                </List>
              ) : (
                <Alert severity="info">
                  This apartment is currently available for rent.
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Booking History */}
      <Box sx={{ mt: 4 }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, display: 'flex', alignItems: 'center' }}>
              <HistoryIcon sx={{ mr: 1 }} />
              Booking History
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {bookingHistory.length > 0 ? (
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Customer</TableCell>
                      <TableCell>Start Date</TableCell>
                      <TableCell>End Date</TableCell>
                      <TableCell>Rent Amount</TableCell>
                      <TableCell>Advance</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Checkout Reason</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {bookingHistory.map((booking) => (
                      <TableRow key={booking.id}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" sx={{ fontWeight: 600 }}>
                              {booking.customer.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {booking.customer.mobile}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{formatDate(booking.startDate)}</TableCell>
                        <TableCell>
                          {booking.endDate ? formatDate(booking.endDate) : (
                            <Chip label="Current" color="success" size="small" />
                          )}
                        </TableCell>
                        <TableCell>{formatCurrency(booking.rentAmount)}</TableCell>
                        <TableCell>{formatCurrency(booking.advanceAmount)}</TableCell>
                        <TableCell>
                          <Chip
                            label={booking.endDate ? 'Completed' : 'Active'}
                            color={booking.endDate ? 'default' : 'success'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {booking.checkoutReason || (booking.endDate ? 'N/A' : '-')}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Alert severity="info">
                No booking history available for this apartment.
              </Alert>
            )}
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

export default ApartmentDetailsPage;
